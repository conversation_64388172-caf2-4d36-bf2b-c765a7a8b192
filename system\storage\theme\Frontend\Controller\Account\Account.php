<?php

namespace Theme25\Frontend\Controller\Account;

class Account extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'account/account');

        // Проверка дали потребителят е логнат
        if (!$this->customer->isLogged()) {
            $this->response->redirect($this->getLink('account/login', '', true));
        }

        // Зареждане на моделите за работа с потребители, адреси и поръчки
        $this->loadModelAs('account/customer', 'customerModel');
        $this->loadModelAs('account/address', 'addressModel');
        $this->loadModelAs('account/order', 'orderModel');
        // Зареждаме специализирания wishlist модел от темата
        $this->loadModelAs('account/wishlist', 'themeWishlistModel');
        $this->loadModelAs('tool/image', 'imageModel');
    }

    public function index() {
        // SEO настройки
        $this->setTitle('Моят профил');
        $this->setDescription('Управлявайте личната си информация и проследявайте поръчките си');

        // Зареждане на JavaScript и CSS файлове
        $this->addFrontendScriptWithVersion('account.js', 'footer');

        // Данни за изпращане към шаблона
        $data = [];

        // 1. Информация за потребителя
        $customer_info = $this->customerModel->getCustomer($this->customer->getId());
        if ($customer_info) {
            $data['customer'] = [
                'firstname' => $customer_info['firstname'],
                'lastname' => $customer_info['lastname'],
                'email' => $customer_info['email'],
                'telephone' => $customer_info['telephone'],
                'newsletter' => $customer_info['newsletter'],
                'date_added' => date('F Y', strtotime($customer_info['date_added']))
            ];
        }

        // 2. Адреси за доставка
        $data['addresses'] = $this->getAddresses();
        $data['default_address_id'] = $this->customer->getAddressId();

        // 3. Последни поръчки
        $data['orders'] = $this->getRecentOrders(5); // Показваме само последните 5 поръчки
        $data['order_total'] = $this->orderModel->getTotalOrders();

        // 4. Любими продукти
        $data['wishlist'] = $this->getWishlistProducts(8); // Лимит до 8 продукта
        $data['wishlist_total'] = $this->getWishlistTotal();

        // 5. Линкове за навигация
        $routes = [
            'account' => 'account/account',
            'address_add' => 'account/address/add',
            'orders' => 'account/order',
            'wishlist' => 'account/wishlist',
            'logout' => 'account/logout'
        ];

        $data['links'] = $this->getLinks($routes);

        // Рендериране на шаблона
        return $this->renderTemplateWithDataAndOutput('account/account', $data);
    }

    /**
     * AJAX метод за обновяване на личната информация
     */
    public function updateInfo() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];
        
        // Валидация на входните данни
        if ((utf8_strlen(trim($this->requestPost('firstname'))) < 1) || (utf8_strlen(trim($this->requestPost('firstname'))) > 32)) {
            $json['error']['firstname'] = 'Името трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('lastname'))) < 1) || (utf8_strlen(trim($this->requestPost('lastname'))) > 32)) {
            $json['error']['lastname'] = 'Фамилията трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen($this->requestPost('email')) > 96) || !filter_var($this->requestPost('email'), FILTER_VALIDATE_EMAIL)) {
            $json['error']['email'] = 'Невалиден имейл адрес!';
        }

        if ((utf8_strlen($this->requestPost('telephone')) < 3) || (utf8_strlen($this->requestPost('telephone')) > 32)) {
            $json['error']['telephone'] = 'Телефонният номер трябва да бъде между 3 и 32 символа!';
        }


        // Проверка дали имейл адресът не се използва от друг потребител
        if ($this->requestPost('email') != $this->customer->getEmail()) {
            $customer_info = $this->customerModel->getCustomerByEmail($this->requestPost('email'));
            if ($customer_info && $customer_info['customer_id'] != $this->customer->getId()) {
                $json['error']['email'] = 'Този имейл адрес вече е регистриран!';
            }
        }

        // Ако няма грешки, обновяваме информацията
        if (empty($json['error'])) {
            $this->customerModel->editCustomer($this->customer->getId(), $this->requestPost());
            $json['success'] = 'Информацията е обновена успешно!';
        }

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за промяна на парола
     */
    public function changePassword() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни
        if ((utf8_strlen(html_entity_decode($this->requestPost('current_password'), ENT_QUOTES, 'UTF-8')) < 4) || (utf8_strlen(html_entity_decode($this->requestPost('current_password'), ENT_QUOTES, 'UTF-8')) > 40)) {
            $json['error']['current_password'] = 'Паролата трябва да бъде между 4 и 40 символа!';
        }

        if (!$this->customerModel->validatePassword($this->requestPost('current_password'), $this->customer->getId())) {
            $json['error']['current_password'] = 'Текущата парола е неправилна!';
        }

        if ((utf8_strlen(html_entity_decode($this->requestPost('new_password'), ENT_QUOTES, 'UTF-8')) < 4) || (utf8_strlen(html_entity_decode($this->requestPost('new_password'), ENT_QUOTES, 'UTF-8')) > 40)) {
            $json['error']['new_password'] = 'Паролата трябва да бъде между 4 и 40 символа!';
        }

        if ($this->requestPost('new_password') != $this->requestPost('confirm_password')) {
            $json['error']['confirm_password'] = 'Паролата за потвърждение не съвпада с новата парола!';
        }

        // Ако няма грешки, променяме паролата
        if (empty($json['error'])) {
            $this->customerModel->editPassword($this->customer->getEmail(), $this->requestPost('new_password'));
            $json['success'] = 'Паролата е променена успешно!';
        }

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за промяна на настройките за известия
     */
    public function updateNotifications() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }
        $json = [];

        $newsletter = $this->requestPost('newsletter') ? 1 : 0;

        $this->customerModel->editNewsletter($newsletter);
        $json['success'] = 'Настройките за известия са обновени!';
        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за получаване на информация за конкретен адрес
     */
    public function addressInfo() {
        // Проверка дали заявката е GET или POST
        if (!$this->isGetRequest() && !$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Получаваме address_id от заявката
        $address_id = $this->requestGet('address_id') ? $this->requestGet('address_id') : $this->requestPost('address_id');

        // Валидация на входните данни
        if (!$address_id) {
            $json['error'] = 'Невалиден адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали адресът принадлежи на потребителя
        $address_info = $this->addressModel->getAddress($address_id);
        if (!$address_info) {
            $json['error'] = 'Нямате достъп до този адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Извличане на alias от custom_field
        $alias = '';
        $custom_field = $address_info['custom_field'];
        if (is_array($custom_field) && isset($custom_field['address_alias'])) {
            $alias = $custom_field['address_alias'];
        }
        
        // Връщаме данните за адреса
        $json['success'] = true;
        $json['address'] = [
            'address_id' => $address_info['address_id'],
            'firstname' => $address_info['firstname'],
            'lastname' => $address_info['lastname'],
            'company' => $address_info['company'],
            'address_1' => $address_info['address_1'],
            'address_2' => $address_info['address_2'],
            'city' => $address_info['city'],
            'postcode' => $address_info['postcode'],
            'zone' => $address_info['zone'],
            'zone_code' => $address_info['zone_code'],
            'zone_id' => $address_info['zone_id'],
            'country' => $address_info['country'],
            'country_id' => $address_info['country_id'],
            'custom_field' => $custom_field,
            'alias' => $alias
        ];

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за добавяне на нов адрес
     */
    public function addAddress() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни
        if ((utf8_strlen(trim($this->requestPost('firstname'))) < 1) || (utf8_strlen(trim($this->requestPost('firstname'))) > 32)) {
            $json['error']['firstname'] = 'Името трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('lastname'))) < 1) || (utf8_strlen(trim($this->requestPost('lastname'))) > 32)) {
            $json['error']['lastname'] = 'Фамилията трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('address_1'))) < 3) || (utf8_strlen(trim($this->requestPost('address_1'))) > 128)) {
            $json['error']['address_1'] = 'Адресът трябва да бъде между 3 и 128 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('city'))) < 2) || (utf8_strlen(trim($this->requestPost('city'))) > 128)) {
            $json['error']['city'] = 'Градът трябва да бъде между 2 и 128 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('postcode'))) < 2) || (utf8_strlen(trim($this->requestPost('postcode'))) > 10)) {
            $json['error']['postcode'] = 'Пощенският код трябва да бъде между 2 и 10 символа!';
        }

        // Ако няма грешки, добавяме адреса
        if (empty($json['error'])) {
            // Обработка на alias полето като custom_field
            $data = $this->requestPost();
            $alias = $this->requestPost('alias');
            
            if ($alias) {
                // Ако имаме alias, добавяме го в custom_field структурата
                $custom_field = isset($data['custom_field']['address']) ? $data['custom_field']['address'] : [];
                $custom_field['address_alias'] = $alias;
                $data['custom_field']['address'] = $custom_field;
            }
            
            $address_id = $this->addressModel->addAddress($this->customer->getId(), $data);

            // Ако е избрано "Използвай като основен адрес"
            if ($this->requestPost('default')) {
                $this->customerModel->editAddressId($this->customer->getId(), $address_id);
            }

            $json['success'] = 'Адресът е добавен успешно!';
            $json['address_id'] = $address_id;
            $json['address_html'] = $this->getAddressHtml($address_id);
        }

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за редактиране на съществуващ адрес
     */
    public function editAddress() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни
        if (!$this->requestPost('address_id')) {
            $json['error'] = 'Невалиден адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали адресът принадлежи на потребителя
        $address_info = $this->addressModel->getAddress($this->requestPost('address_id'));
        if (!$address_info) {
            $json['error'] = 'Нямате достъп до този адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Валидация на полетата
        if ((utf8_strlen(trim($this->requestPost('firstname'))) < 1) || (utf8_strlen(trim($this->requestPost('firstname'))) > 32)) {
            $json['error']['firstname'] = 'Името трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('lastname'))) < 1) || (utf8_strlen(trim($this->requestPost('lastname'))) > 32)) {
            $json['error']['lastname'] = 'Фамилията трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('address_1'))) < 3) || (utf8_strlen(trim($this->requestPost('address_1'))) > 128)) {
            $json['error']['address_1'] = 'Адресът трябва да бъде между 3 и 128 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('city'))) < 2) || (utf8_strlen(trim($this->requestPost('city'))) > 128)) {
            $json['error']['city'] = 'Градът трябва да бъде между 2 и 128 символа!';
        }

        if ((utf8_strlen(trim($this->requestPost('postcode'))) < 2) || (utf8_strlen(trim($this->requestPost('postcode'))) > 10)) {
            $json['error']['postcode'] = 'Пощенският код трябва да бъде между 2 и 10 символа!';
        }

        // Ако няма грешки, редактираме адреса
        if (empty($json['error'])) {
            // Обработка на alias полето като custom_field
            $data = $this->requestPost();
            $alias = $this->requestPost('alias');
            
            if ($alias) {
                // Ако имаме alias, добавяме го в custom_field структурата
                $custom_field = isset($data['custom_field']['address']) ? $data['custom_field']['address'] : [];
                $custom_field['address_alias'] = $alias;
                $data['custom_field']['address'] = $custom_field;
            }
            
            $this->addressModel->editAddress($this->requestPost('address_id'), $data);

            // Ако е избрано "Използвай като основен адрес"
            if ($this->requestPost('default')) {
                $this->customerModel->editAddressId($this->customer->getId(), $this->requestPost('address_id'));
            }

            $json['success'] = 'Адресът е редактиран успешно!';
            $json['address_html'] = $this->getAddressHtml($this->requestPost('address_id'));
        }

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за изтриване на адрес
     */
    public function deleteAddress() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни
        if (!$this->requestPost('address_id')) {
            $json['error'] = 'Невалиден адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали адресът принадлежи на потребителя
        $address_info = $this->addressModel->getAddress($this->requestPost('address_id'), $this->customer->getId());
        if (!$address_info) {
            $json['error'] = 'Нямате достъп до този адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали това е основният адрес
        if ($this->customer->getAddressId() == $this->requestPost('address_id')) {
            $json['error'] = 'Не можете да изтриете основния си адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Изтриване на адреса
        $this->addressModel->deleteAddress($this->requestPost('address_id'));
        $json['success'] = 'Адресът е изтрит успешно!';

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за задаване на адрес като основен
     */
    public function setDefaultAddress() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни
        if (!$this->requestPost('address_id')) {
            $json['error'] = 'Невалиден адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали адресът принадлежи на потребителя
        $address_info = $this->addressModel->getAddress($this->requestPost('address_id'));
        if (!$address_info) {
            $json['error'] = 'Нямате достъп до този адрес!';
            $this->jsonResponse($json);
            return;
        }

        // Задаване на адреса като основен използвайки метода editAddressId от модела customer
        $this->customerModel->editAddressId($this->customer->getId(), $this->requestPost('address_id'));
        $json['success'] = 'Основният адрес е променен успешно!';

        $this->jsonResponse($json);
    }

    /**
     * AJAX метод за изтриване на профил
     */
    public function deleteAccount() {
        // Проверка дали заявката е POST
        if (!$this->isPostRequest()) {
            $this->jsonResponse(['error' => 'Невалиден метод на заявка']);
            return;
        }

        $json = [];

        // Валидация на входните данни - изискваме парола за потвърждение
        if (!$this->requestPost('password')) {
            $json['error'] = 'Моля, въведете паролата си за потвърждение!';
            $this->jsonResponse($json);
            return;
        }

        // Проверка дали паролата е правилна
        if (!$this->customerModel->validatePassword($this->requestPost('password'), $this->customer->getId())) {
            $json['error'] = 'Въведената парола е неправилна!';
            $this->jsonResponse($json);
            return;
        }

        // Изтриване на профила
        $this->customerModel->deleteCustomer($this->customer->getId());
        $this->customer->logout();
        
        $json['success'] = 'Профилът е изтрит успешно!';
        $json['redirect'] = $this->getLink('account/login');

        $this->jsonResponse($json);
    }

    /**
     * Помощен метод за получаване на адресите на потребителя
     */
    private function getAddresses() {
        $addresses = [];
        $results = $this->addressModel->getAddresses();

        foreach ($results as $result) {
            // Извличане на alias от custom_field
            $custom_field = $result['custom_field'];
            $alias = '';
            
            if (is_array($custom_field) && isset($custom_field['address_alias'])) {
                $alias = $custom_field['address_alias'];
            }
            
            $addresses[] = [
                'address_id' => $result['address_id'],
                'firstname' => $result['firstname'],
                'lastname' => $result['lastname'],
                'company' => $result['company'],
                'address_1' => $result['address_1'],
                'address_2' => $result['address_2'],
                'city' => $result['city'],
                'postcode' => $result['postcode'],
                'zone' => $result['zone'],
                'zone_code' => $result['zone_code'],
                'country' => $result['country'],
                'custom_field' => $custom_field,
                'alias' => $alias
            ];
        }

        return $addresses;
    }

    /**
     * Помощен метод за получаване на HTML представяне на адрес
     */
    private function getAddressHtml($address_id) {
        $address_info = $this->addressModel->getAddress($address_id);

        if ($address_info) {
            // Извличане на alias от custom_field
            $alias = '';
            if (isset($address_info['custom_field']) && is_array($address_info['custom_field']) && isset($address_info['custom_field']['address_alias'])) {
                $alias = $address_info['custom_field']['address_alias'];
            }
            
            // Минимално HTML представяне на адреса
            $html = '<div class="address-item">';
            if ($alias) {
                $html .= '<p class="text-gray-600 mb-1">' . $alias . '</p>';
            }
            $html .= '<p><strong>' . $address_info['firstname'] . ' ' . $address_info['lastname'] . '</strong></p>';
            $html .= '<p>' . $address_info['address_1'] . '</p>';
            if ($address_info['address_2']) {
                $html .= '<p>' . $address_info['address_2'] . '</p>';
            }
            $html .= '<p>' . $address_info['city'] . ', ' . $address_info['postcode'] . '</p>';
            $html .= '<p>' . $address_info['country'] . '</p>';
            $html .= '</div>';

            return $html;
        }

        return '';
    }

    /**
     * Помощен метод за получаване на последните поръчки
     */
    private function getRecentOrders($limit = 5) {
        $orders = [];
        $results = $this->orderModel->getOrders(0, $limit);

        foreach ($results as $result) {
            $orders[] = [
                'order_id' => $result['order_id'],
                'name' => $result['firstname'] . ' ' . $result['lastname'],
                'status' => $result['status'],
                'date_added' => date('d.m.Y', strtotime($result['date_added'])),
                'products' => $this->orderModel->getOrderProducts($result['order_id']),
                'total' => $this->currency->format($result['total'], $result['currency_code'], $result['currency_value']),
                'view' => $this->getLink('account/order/info', 'order_id=' . $result['order_id']),
            ];
        }

        return $orders;
    }

    /**
     * Помощен метод за получаване на любими продукти
     */
    private function getWishlistProducts($limit = 8) {
        $wishlist = [];

        if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
            $results = $this->themeWishlistModel->getWishlist(0, $limit);
            foreach ($results as $result) {
                // Зареждаме изображението
                $image = '';
                if ($result['image']) {
                    $this->loadModelAs('tool/image', 'imageModel');
                    $image = $this->imageModel->resize($result['image'], 600, 600);
                }

                $wishlist[] = [
                    'product_id' => $result['product_id'],
                    'name' => $result['name'],
                    'model' => $result['model'],
                    'image' => $image,
                    'price' => $result['price'], // Сурова цена
                    'special' => $result['special'], // Сурова специална цена
                    'price_formatted' => $this->formatCurrency($result['price']),
                    'special_formatted' => $result['special'] ? $this->formatCurrency($result['special']) : null,
                    'href' => $this->getLink('product/product', 'product_id=' . $result['product_id']),
                    'date_added' => $result['date_added']
                ];
            }
        }

        return $wishlist;
    }

    /**
     * Помощен метод за получаване на общ брой любими продукти
     */
    private function getWishlistTotal() {
        $total = 0;

        if (is_callable([$this->themeWishlistModel, 'getTotalWishlist'])) {
            $total = $this->themeWishlistModel->getTotalWishlist();
        }

        return $total;
    }

    /**
     * AJAX метод за зареждане на още продукти от wishlist
     */
    public function loadMore() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            // Получаване на параметрите за пагинация
            $page = max(1, (int)$this->requestPost('page', 1));
            $limit = max(1, (int)$this->requestPost('limit', 8));
            $offset = ($page - 1) * $limit;
            $sort = $this->requestPost('sort', 'newest');

            // Получаване на продуктите с offset и limit
            if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
                $results = $this->themeWishlistModel->getWishlist($offset, $limit, $sort);

                $products = [];
                foreach ($results as $result) {
                    // Зареждаме изображението
                    $image = '';
                    if ($result['image']) {
                        $this->loadModelAs('tool/image', 'imageModel');
                        $image = $this->imageModel->resize($result['image'], 600, 600);
                    }

                    $products[] = [
                        'product_id' => $result['product_id'],
                        'name' => $result['name'],
                        'model' => $result['model'],
                        'image' => $image,
                        'price' => $result['price'], // Сурова цена
                        'special' => $result['special'], // Сурова специална цена
                        'price_formatted' => $this->formatCurrency($result['price']),
                        'special_formatted' => $result['special'] ? $this->formatCurrency($result['special']) : null,
                        'href' => $this->getLink('product/product', 'product_id=' . $result['product_id']),
                        'date_added' => $result['date_added']
                    ];
                }

                $total = $this->getWishlistTotal();
                $loaded = $offset + count($products);

                $json['success'] = true;
                $json['products'] = $products;
                $json['total'] = $total;
                $json['loaded'] = $loaded;
                $json['has_more'] = $loaded < $total;
            } else {
                throw new \Exception('Функцията за зареждане на wishlist продукти не е налична');
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за добавяне на продукт в wishlist
     */
    public function add() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');

            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            // Зареждаме модела за продукти за валидация
            $this->loadModelAs('catalog/product', 'productModel');
            $product_info = $this->productModel->getProduct($product_id);

            if (!$product_info) {
                throw new \Exception('Продуктът не е намерен');
            }

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - добавяме в базата данни

                // Проверяваме дали продуктът вече е в wishlist
                if ($this->themeWishlistModel->inWishlist($product_id)) {
                    $json['success'] = false;
                    $json['message'] = 'Продуктът вече е в списъка с любими продукти';
                    $json['already_exists'] = true;
                } else {
                    // Добавяме продукта в wishlist
                    $this->themeWishlistModel->addWishlist($product_id);

                    $json['success'] = true;
                    $json['message'] = 'Продуктът е добавен в списъка с любими продукти';
                    $json['product_name'] = $product_info['name'];

                    // Получаваме актуализирания брой продукти в wishlist
                    $json['wishlist_count'] = $this->themeWishlistModel->getTotalWishlist();
                }

            } else {
                // Потребителят не е логнат - използваме сесията
                if (!isset($this->session->data['wishlist'])) {
                    $this->session->data['wishlist'] = [];
                }

                if (in_array($product_id, $this->session->data['wishlist'])) {
                    $json['success'] = false;
                    $json['message'] = 'Продуктът вече е в списъка с любими продукти';
                    $json['already_exists'] = true;
                } else {
                    $this->session->data['wishlist'][] = $product_id;
                    $this->session->data['wishlist'] = array_unique($this->session->data['wishlist']);

                    $json['success'] = true;
                    $json['message'] = 'Продуктът е добавен в списъка с любими продукти';
                    $json['product_name'] = $product_info['name'];
                    $json['login_required'] = true;
                    $json['login_message'] = 'За да запазите списъка с любими продукти, моля влезте в профила си';

                    // Брой продукти в сесийния wishlist
                    $json['wishlist_count'] = count($this->session->data['wishlist']);
                }
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за премахване на продукт от wishlist
     */
    public function remove() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');

            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - премахваме от базата данни
                $this->themeWishlistModel->deleteWishlist($product_id);

                $json['success'] = true;
                $json['message'] = 'Продуктът е премахнат от списъка с любими продукти';

                // Получаваме актуализирания брой продукти в wishlist
                $json['wishlist_count'] = $this->themeWishlistModel->getTotalWishlist();

            } else {
                // Потребителят не е логнат - премахваме от сесията
                if (isset($this->session->data['wishlist'])) {
                    $key = array_search($product_id, $this->session->data['wishlist']);
                    if ($key !== false) {
                        unset($this->session->data['wishlist'][$key]);
                        $this->session->data['wishlist'] = array_values($this->session->data['wishlist']);
                    }
                }

                $json['success'] = true;
                $json['message'] = 'Продуктът е премахнат от списъка с любими продукти';

                // Брой продукти в сесийния wishlist
                $json['wishlist_count'] = isset($this->session->data['wishlist']) ? count($this->session->data['wishlist']) : 0;
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за проверка дали продукт е в wishlist
     */
    public function check() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');

            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            $in_wishlist = false;

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - проверяваме в базата данни
                $in_wishlist = $this->themeWishlistModel->inWishlist($product_id);

            } else {
                // Потребителят не е логнат - проверяваме в сесията
                if (isset($this->session->data['wishlist'])) {
                    $in_wishlist = in_array($product_id, $this->session->data['wishlist']);
                }
            }

            $json['success'] = true;
            $json['in_wishlist'] = $in_wishlist;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за получаване на броя продукти в wishlist
     */
    public function getCount() {
        $json = [];

        try {
            $count = 0;

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - броим от базата данни
                $count = $this->themeWishlistModel->getTotalWishlist();
            } else {
                // Потребителят не е логнат - броим от сесията
                $count = isset($this->session->data['wishlist']) ? count($this->session->data['wishlist']) : 0;
            }

            $json['success'] = true;
            $json['count'] = $count;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за проверка на множество продукти в wishlist наведнъж (batch операция)
     */
    public function checkMultiple() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_ids = $this->requestPost('product_ids');

            // Ако product_ids е подаден като JSON string, декодираме го
            if (is_string($product_ids)) {
                $product_ids = json_decode($product_ids, true);
            }

            if (!is_array($product_ids) || empty($product_ids)) {
                throw new \Exception('Невалидни product_ids');
            }

            // Валидираме и почистваме product_ids
            $valid_product_ids = [];
            foreach ($product_ids as $id) {
                $id = (int)$id;
                if ($id > 0) {
                    $valid_product_ids[] = $id;
                }
            }

            if (empty($valid_product_ids)) {
                throw new \Exception('Няма валидни product_ids');
            }

            $wishlist_status = [];

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - използваме batch проверка от новия модел
                $wishlist_product_ids = $this->themeWishlistModel->getWishlistByProductIds($valid_product_ids);

                // Проверяваме всеки продукт
                foreach ($valid_product_ids as $product_id) {
                    $wishlist_status[$product_id] = in_array($product_id, $wishlist_product_ids);
                }

            } else {
                // Потребителят не е логнат - проверяваме в сесията
                $session_wishlist = isset($this->session->data['wishlist']) ? $this->session->data['wishlist'] : [];

                foreach ($valid_product_ids as $product_id) {
                    $wishlist_status[$product_id] = in_array($product_id, $session_wishlist);
                }
            }

            $json['success'] = true;
            $json['wishlist_status'] = $wishlist_status;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }
}
