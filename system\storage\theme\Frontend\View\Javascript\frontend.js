/**
 * Основен JavaScript модул за frontend-а
 */
(function() {
    'use strict';

    // Инициализация на модула
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на всички компоненти
        FrontendModule.init();
    });

    // Основен обект с функционалности
    var FrontendModule = {
        // Конфигурация
        config: {
            requestTimeout: 30000 // 30 секунди по подразбиране
        },

        init: function() {
            this.initChat();
            this.initMegaMenu();
            this.initHeroBanner();
        },

        // Функционалност за чат бъбъла
        initChat: function() {
            const chatBubble = document.getElementById('chatBubble');
            const closeChatBtn = document.getElementById('closeChatBtn');

            // Utility function to find button by text content
            function contains(selector, text) {
                var elements = document.querySelectorAll(selector);
                return Array.prototype.filter.call(elements, function(element){
                    return RegExp(text).test(element.textContent);
                });
            }

            // Find the "Питай Бети" button
            const bettyBtn = contains('button', 'Питай Бети')[0];

            if (bettyBtn && chatBubble) {
                bettyBtn.addEventListener('click', function() {
                    chatBubble.classList.add('active');
                });
            }

            if (closeChatBtn && chatBubble) {
                closeChatBtn.addEventListener('click', function() {
                    chatBubble.classList.remove('active');
                });
            }
        },

        // Функционалност за мега менюто
        initMegaMenu: function() {
            const menuItems = document.querySelectorAll('.menu-item');
            const megaMenus = document.querySelectorAll('.mega-menu-container > div');
            const overlay = document.querySelector('.overlay-backdrop');
            let activeMenu = null;

            menuItems.forEach((item) => {
                const link = item.querySelector('a');
                if (link && link.textContent) {
                    const menuText = link.textContent.trim().toLowerCase();
                    const hasSubmenu = link.querySelector('i') !== null;

                    if (hasSubmenu) {
                        const matchingMenu = Array.from(megaMenus).find(menu => {
                            const menuId = menu.getAttribute('data-menu');
                            return menuId && menuText.includes(menuId.replace(/-/g, ' '));
                        });

                        if (matchingMenu) {
                            item.addEventListener('mouseenter', () => {
                                this.showMegaMenu(matchingMenu, activeMenu);
                                activeMenu = matchingMenu;
                            });

                            matchingMenu.addEventListener('mouseenter', () => {
                                this.showMegaMenu(matchingMenu, activeMenu);
                                activeMenu = matchingMenu;
                            });

                            matchingMenu.addEventListener('mouseleave', () => {
                                this.hideMegaMenu(matchingMenu);
                            });
                        }
                    }
                }
            });

            // Скриване на всички менюта при напускане на навигацията
            const menuContainer = document.querySelector('.menu-container');
            if (menuContainer) {
                menuContainer.addEventListener('mouseleave', () => {
                    megaMenus.forEach(menu => {
                        menu.classList.add('hidden');
                    });
                });
            }

            // Плавни преходи за подкатегориите
            this.initSubmenuEffects();

            // Ефекти за изображенията в категориите
            this.initCategoryImageEffects();
        },

        // Показване на мега меню
        showMegaMenu: function(menuToShow, currentActiveMenu) {
            if (currentActiveMenu) {
                currentActiveMenu.classList.remove('active');
                currentActiveMenu.classList.add('hidden');
            }
            menuToShow.classList.remove('hidden');
            setTimeout(() => {
                menuToShow.classList.add('active');
            }, 50);
        },

        // Скриване на мега меню
        hideMegaMenu: function(menu) {
            menu.classList.remove('active');
            setTimeout(() => {
                menu.classList.add('hidden');
            }, 300);
        },

        // Ефекти за подменютата
        initSubmenuEffects: function() {
            const submenuItems = document.querySelectorAll('.submenu-item');
            submenuItems.forEach(item => {
                item.addEventListener('mouseenter', () => {
                    item.classList.add('text-primary');
                });
                item.addEventListener('mouseleave', () => {
                    if (!item.classList.contains('active')) {
                        item.classList.remove('text-primary');
                    }
                });
            });
        },

        // Ефекти за изображенията в категориите
        initCategoryImageEffects: function() {
            const categoryLinks = document.querySelectorAll('.category-link');
            categoryLinks.forEach(link => {
                const image = link.querySelector('.category-image');
                const overlay = link.querySelector('.category-overlay');

                link.addEventListener('mouseenter', () => {
                    if (image) image.style.transform = 'scale(1.05)';
                    if (overlay) overlay.style.opacity = '0.1';
                });

                link.addEventListener('mouseleave', () => {
                    if (image) image.style.transform = 'scale(1)';
                    if (overlay) overlay.style.opacity = '0';
                });
            });
        },



        initHeroBanner: function() {
            if (typeof Swiper === 'undefined') return;
            if(!document.querySelector('.swiper-container')) return;
            const slider_settings = window.heroBannerSettings || {};
            const swiper = new Swiper('.swiper-container', slider_settings);
            swiper.el.addEventListener('mouseenter', () => {
                swiper.autoplay.stop();
            });
            swiper.el.addEventListener('mouseleave', () => {
                swiper.autoplay.start();
            });
        },

        // Помощни функции
        helpers: {
            getUrlVars: function() {
                var vars = [], hash;
                var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
                for(var i = 0; i < hashes.length; i++) {
                    hash = hashes[i].split('=');
                    vars.push(hash[0]);
                    vars[hash[0]] = hash[1];
                }
                return vars;
            },

            getUrlVar: function(name) {
                return this.getUrlVars()[name];
            }
        },

        /**
         * Прави AJAX заявка с използване на fetch API
         * @param {string} url - URL адрес за заявката
         * @param {FormData} formData - Данни за изпращане
         * @param {Function} successCallback - Функция за успешен отговор
         * @param {Function} errorCallback - Функция за грешка
         */
        makeAjaxRequest: function(url, formData, successCallback, errorCallback) {
            // Проверка дали slide cart е отворен и добавяне на параметър
            const slideCart = document.querySelector('.slide-cart');
            const slideCartIsOpen = slideCart && slideCart.classList.contains('active');
            
            // Ако formData е FormData, добавяме параметъра като част от формата
            if (formData instanceof FormData) {
                if (slideCartIsOpen) {
                    formData.append('slide_cart_is_open', '1');
                }
            } else if (formData && typeof formData === 'object') {
                // Ако е обект, добавяме параметъра към обекта
                if (slideCartIsOpen) {
                    formData.slide_cart_is_open = '1';
                }
                // Ако е обект, преобразуваме го в JSON стринг
                formData = JSON.stringify(formData);
            }
            
            // Проверка дали formData е обект, но не е FormData
            if (formData && typeof formData === 'object' && !(formData instanceof FormData)) {
                // Ако е обект, преобразуваме го в JSON стринг
                formData = JSON.stringify(formData);
            }

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && typeof successCallback === 'function') {
                    successCallback(data);
                } else if (typeof errorCallback === 'function') {
                    if(typeof data.error === 'string')
                        errorCallback(new Error(data.error));
                    else if(typeof data.message === 'string')
                        errorCallback(new Error(data.message));
                    else
                        errorCallback(data);
                }
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                if (typeof errorCallback === 'function') {
                    errorCallback(error);
                }
            });
        },

        showNotification: function(message, type = 'info') {
            if (typeof this.showAlert === 'function') {
                this.showAlert(type, message);
            }
        },

        /**
         * Форматира валута според настройките
         * @param {number} amount - Сумата за форматиране
         * @param {Object} currencyConfig - Конфигурация за валутите
         * @returns {string} - Форматирана цена
         */
        formatCurrency: function(amount, currencyConfig) {
            if (!currencyConfig || typeof amount !== 'number') {
                return amount + ' лв.'; // Fallback
            }

            const config = currencyConfig;
            const displayFormat = config.display_format || 'bgn';

            // Форматиране според настройката
            switch (displayFormat) {
                case 'bgn':
                    return this.formatSingleCurrency(amount, config.bgn);

                case 'eur':
                    return this.formatSingleCurrency(amount, config.eur);

                case 'bgn-eur':
                    const bgnFormatted = this.formatSingleCurrency(amount, config.bgn);
                    const eurAmount = amount * (config.eur.value || 0.5113);
                    const eurFormatted = this.formatSingleCurrencyConverted(eurAmount, config.eur);
                    return bgnFormatted + ' / ' + eurFormatted;

                case 'eur-bgn':
                    const eurAmountPrimary = amount * (config.eur.value || 0.5113);
                    const eurFormattedPrimary = this.formatSingleCurrencyConverted(eurAmountPrimary, config.eur);
                    const bgnFormattedSecondary = this.formatSingleCurrency(amount, config.bgn);
                    return eurFormattedPrimary + ' / ' + bgnFormattedSecondary;

                default:
                    return this.formatSingleCurrency(amount, config.bgn);
            }
        },

        /**
         * Форматира цена в една валута
         * @param {number} amount - Сумата за форматиране
         * @param {Object} currencyData - Данни за валутата
         * @returns {string} - Форматирана цена
         */
        formatSingleCurrency: function(amount, currencyData) {
            if (!currencyData) {
                return amount.toFixed(2) + ' лв.';
            }

            const value = currencyData.value || 1;
            const convertedAmount = amount * value;
            const decimalPlaces = currencyData.decimal_place || 2;
            const formattedAmount = convertedAmount.toFixed(decimalPlaces);

            // Форматиране с хиляди разделители
            const parts = formattedAmount.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
            const finalAmount = parts.join('.');

            // Добавяне на символи
            let result = '';
            if (currencyData.symbol_left) {
                result += currencyData.symbol_left;
            }
            result += finalAmount;
            if (currencyData.symbol_right) {
                result += currencyData.symbol_right;
            }

            return result;
        },

        /**
         * Форматира вече конвертирана цена (без допълнително конвертиране)
         * @param {number} convertedAmount - Вече конвертираната сума
         * @param {Object} currencyData - Данни за валутата
         * @returns {string} - Форматирана цена
         */
        formatSingleCurrencyConverted: function(convertedAmount, currencyData) {
            if (!currencyData) {
                return convertedAmount.toFixed(2) + ' лв.';
            }

            const decimalPlaces = currencyData.decimal_place || 2;
            const formattedAmount = convertedAmount.toFixed(decimalPlaces);

            // Форматиране с хиляди разделители
            const parts = formattedAmount.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
            const finalAmount = parts.join('.');

            // Добавяне на символи
            let result = '';
            if (currencyData.symbol_left) {
                result += currencyData.symbol_left;
            }
            result += finalAmount;
            if (currencyData.symbol_right) {
                result += currencyData.symbol_right;
            }

            return result;
        },

        // Debug функция
        logDev: function(message, data) {
            if (console && console.log) {
                if (data !== undefined) {
                    console.log('[FrontendModule]', message, data);
                } else {
                    console.log('[FrontendModule]', message);
                }
            }
        }
    };

    // Експортиране на модула за глобален достъп
    window.FrontendModule = FrontendModule;

})();