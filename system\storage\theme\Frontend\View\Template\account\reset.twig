<main class="min-h-screen py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-5xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="flex flex-col md:flex-row">
                <!-- Лява страна - Декоративно изображение -->
                <div class="w-full md:w-2/5 relative hidden md:block">
                    <div class="absolute inset-0" style="background-image: url('https://rakla.bg/image/catalog/account/login-background.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="absolute inset-0 bg-primary opacity-20"></div>
                </div>
                <!-- Д<PERSON><PERSON>на страна - Форма за възстановяване на парола -->
                <div class="w-full md:w-3/5 p-8 md:p-12">
                    <div class="max-w-md mx-auto">
                        <h1 class="text-3xl font-bold text-gray-800 mb-8">Възстановяване на парола</h1>

                        {% if error_code %}
                            <div class="text-red-500 text-sm mt-1 py-2">{{ error_code }}</div>
                        {% else %}
                        
                        <form action="{{ links.action }}" method="post" enctype="multipart/form-data" class="space-y-6">
                            <input type="hidden" name="code" value="{{ code }}">
                            
                            <!-- Поле за нова парола -->
                            <div>
                                <label for="input-password" class="block text-sm font-medium text-gray-700 mb-1">Нова парола</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="ri-lock-line text-gray-400"></i>
                                    </div>
                                    <input
                                        type="password"
                                        id="input-password"
                                        name="password"
                                        class="form-input w-full pl-10 pr-12 py-3 border {{ error_password ? 'border-red-500' : 'border-gray-300' }} rounded-lg focus:outline-none text-sm"
                                        placeholder="••••••••"
                                        required
                                        value="{{ password }}"
                                    >
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" class="password-toggle text-gray-400 focus:outline-none">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                </div>
                                {% if error_password %}
                                <div class="text-red-500 text-sm mt-1">{{ error_password }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Поле за потвърждаване на паролата -->
                            <div>
                                <label for="input-confirm" class="block text-sm font-medium text-gray-700 mb-1">Потвърдете паролата</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="ri-lock-line text-gray-400"></i>
                                    </div>
                                    <input
                                        type="password"
                                        id="input-confirm"
                                        name="confirm"
                                        class="form-input w-full pl-10 pr-12 py-3 border {{ error_confirm ? 'border-red-500' : 'border-gray-300' }} rounded-lg focus:outline-none text-sm"
                                        placeholder="••••••••"
                                        required
                                        value="{{ confirm }}"
                                    >
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" class="password-toggle text-gray-400 focus:outline-none">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                </div>
                                {% if error_confirm %}
                                <div class="text-red-500 text-sm mt-1">{{ error_confirm }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Бутони за действие -->
                            <div class="flex items-center justify-between pt-4">
                                <a href="{{ links.login }}" class="inline-block text-primary font-medium hover:text-primary/80 transition-colors">
                                    <i class="ri-arrow-left-line mr-1"></i> Обратно към вход
                                </a>
                                <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors shadow-lg shadow-primary/20">
                                    Промяна на паролата
                                </button>
                            </div>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализация на показване/скриване на парола
        const passwordToggles = document.querySelectorAll('.password-toggle');
        
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const input = this.closest('.relative').querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('ri-eye-line');
                    icon.classList.add('ri-eye-off-line');
                } else {
                    input.type = 'password';
                    icon.classList.remove('ri-eye-off-line');
                    icon.classList.add('ri-eye-line');
                }
            });
        });
    });
</script>
