<style>
.profile-section {
    display: none;
}

.profile-section.active {
    display: block;
}

.profile-tab {
    color: #6B7280;
    border-bottom-color: transparent;
}

.profile-tab.active {
    color: #1F2937;
    border-bottom-color: var(--primary-color, #8B5CF6);
}

.profile-tab:hover {
    color: #374151;
}
</style>

<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="mx-auto">
            <!-- Page Title -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-800">Моят профил</h1>
                <p class="text-gray-600 mt-2">Управлявайте личната си информация и проследявайте поръчките си</p>
            </div>
            
            <!-- Profile Navigation Tabs -->
            <div class="mb-8 border-b border-gray-200">
                <div class="flex flex-wrap -mb-px">
                    <button class="profile-tab active py-4 px-6 font-medium text-gray-800 border-b-2 border-primary" data-tab="personal-info">
                        <i class="ri-user-line mr-2"></i>Лична информация
                    </button>
                    <button class="profile-tab py-4 px-6 font-medium text-gray-500 border-b-2 border-transparent" data-tab="addresses">
                        <i class="ri-map-pin-line mr-2"></i>Адреси
                    </button>
                    <button class="profile-tab py-4 px-6 font-medium text-gray-500 border-b-2 border-transparent" data-tab="orders">
                        <i class="ri-shopping-bag-line mr-2"></i>История на поръчките
                    </button>
                    <button class="profile-tab py-4 px-6 font-medium text-gray-500 border-b-2 border-transparent" data-tab="favorites">
                        <i class="ri-heart-line mr-2"></i>Любими продукти
                    </button>
                    <button class="profile-tab py-4 px-6 font-medium text-gray-500 border-b-2 border-transparent" data-tab="settings">
                        <i class="ri-settings-line mr-2"></i>Настройки
                    </button>
                    <button class="flex items-center py-4 px-6 font-medium text-red-600 border-b-2 border-transparent hover:text-red-700" id="logoutButton">
                        <i class="ri-logout-box-line mr-2"></i>Изход
                    </button>
                </div>
            </div>
            
            <!-- Profile Content Sections -->
            <div class="bg-white rounded-lg shadow-sm pb-4">
                <!-- Personal Information Section -->
                <div class="profile-section active" id="personal-info">
                    <div class="flex flex-col md:flex-row gap-12">
                        <!-- Profile Info -->
                        <div class="w-full md:w-1/3 flex flex-col">
                            <h3 class="text-xl font-semibold text-gray-800 mb-1">{{ customer.firstname }} {{ customer.lastname }}</h3>
                            <p class="text-gray-500 mb-4">Клиент от {{ customer.date_added }}</p>
                            <div class="flex flex-col w-full space-y-3">
                                <div class="flex items-center text-gray-600">
                                    <div class="w-6 h-6 flex items-center justify-center mr-3">
                                        <i class="ri-mail-line"></i>
                                    </div>
                                    <span>{{ customer.email }}</span>
                                </div>
                                <div class="flex items-center text-gray-600 mb-2">
                                    <div class="w-6 h-6 flex items-center justify-center mr-3">
                                        <i class="ri-phone-line"></i>
                                    </div>
                                    <span>{{ customer.telephone }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Personal Info Form -->
                        <div class="w-full md:w-2/3">
                            <h2 class="text-2xl font-semibold text-gray-800 mb-6">Редактиране на профила</h2>
                            <form id="personalInfoForm" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                                        <input type="text" id="firstname" name="firstname" value="{{ customer.firstname }}" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                    </div>
                                    <div>
                                        <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">Фамилия</label>
                                        <input type="text" id="lastname" name="lastname" value="{{ customer.lastname }}" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                    </div>
                                    <div>
                                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Имейл адрес</label>
                                        <input type="email" id="email" name="email" value="{{ customer.email }}" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                    </div>
                                    <div>
                                        <label for="telephone" class="block text-sm font-medium text-gray-700 mb-1">Телефон</label>
                                        <input type="tel" id="telephone" name="telephone" value="{{ customer.telephone }}" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                    </div>
                                </div>
                                <div class="pt-4">
                                    <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors shadow-md shadow-primary/20 whitespace-nowrap">
                                        Запази промените
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Addresses Section (частична реализация) -->
                <div class="profile-section" id="addresses">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800">Адреси за доставка</h2>
                        <button id="addAddressBtn" class="bg-primary text-white px-4 py-2 rounded-button font-medium hover:bg-opacity-90 transition-colors flex items-center whitespace-nowrap">
                            <i class="ri-add-line mr-2"></i>Добави нов адрес
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="addressesContainer">
                    {% if addresses %}
                        {% for address in addresses %}
                        <div class="border border-gray-200 rounded-lg p-6 relative hover:shadow-md transition-shadow" data-address-id="{{ address.address_id }}">
                            <div class="absolute top-4 right-4 flex space-x-2">
                                <button class="edit-address-btn w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                                    <i class="ri-pencil-line"></i>
                                </button>
                                <button class="delete-address-btn w-8 h-8 flex items-center justify-center text-gray-500 hover:text-red-500 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                            <div class="flex items-start mb-4">
                                <div class="mr-3">
                                    <label class="custom-radio">
                                        <input type="radio" name="default-address" class="default-address-radio" value="{{ address.address_id }}" {% if address.address_id == default_address_id %}checked{% endif %}>
                                        <span class="radio-mark"></span>
                                    </label>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-lg text-gray-800">{% if address.alias %}{{ address.alias }}{% else %}Адрес {{ loop.index }}{% endif %}</h3>
                                    {% if address.address_id == default_address_id %}
                                    <p class="text-gray-600 text-sm">Използва се по подразбиране</p>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="ml-8">
                                <p class="font-medium text-gray-800 mb-1">{{ address.firstname }} {{ address.lastname }}</p>
                                <p class="text-gray-600 mb-1">{{ address.address_1 }}</p>
                                {% if address.address_2 %}
                                <p class="text-gray-600 mb-1">{{ address.address_2 }}</p>
                                {% endif %}
                                <p class="text-gray-600 mb-1">{{ address.city }}, {{ address.postcode }}</p>
                                <p class="text-gray-600 mb-1">{{ address.zone }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="col-span-2 p-8 text-center">
                            <p class="text-gray-500">Нямате запазени адреси.</p>
                            <button id="noAddressesBtn" class="mt-4 bg-primary text-white px-4 py-2 rounded-button font-medium hover:bg-opacity-90 transition-colors flex items-center mx-auto">
                                <i class="ri-add-line mr-2"></i>Добави нов адрес
                            </button>
                        </div>
                    {% endif %}
                    </div>
                </div>

                <!-- Orders History Section -->
                <div class="profile-section" id="orders">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800">История на поръчките</h2>
                        <div class="flex space-x-4">
                            <div class="relative">
                                <input type="text" placeholder="Търсене по номер..." class="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20">
                                <button class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-primary">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                            <div class="relative">
                                <select class="appearance-none px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 pr-8">
                                    <option value="">Всички статуси</option>
                                    <option value="processing">В обработка</option>
                                    <option value="shipped">Изпратена</option>
                                    <option value="delivered">Доставена</option>
                                    <option value="cancelled">Отказана</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <i class="ri-arrow-down-s-line text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if orders and orders|length > 0 %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Номер</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Дата</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Сума</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Детайли</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for order in orders %}
                                <tr class="order-row cursor-pointer">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">#{{ order.order_id }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">{{ order.date_added }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% set status_class = 'bg-gray-100 text-gray-800' %}
                                        {% if order.status == 'Доставена' %}
                                            {% set status_class = 'bg-green-100 text-green-800' %}
                                        {% elseif order.status == 'Изпратена' %}
                                            {% set status_class = 'bg-blue-100 text-blue-800' %}
                                        {% elseif order.status == 'В обработка' %}
                                            {% set status_class = 'bg-yellow-100 text-yellow-800' %}
                                        {% elseif order.status == 'Отказана' %}
                                            {% set status_class = 'bg-red-100 text-red-800' %}
                                        {% endif %}
                                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {{ status_class }}">{{ order.status }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ order.total }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="{{ order.view }}" class="text-primary hover:text-primary-dark">
                                            <i class="ri-eye-line mr-1"></i> Детайли
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if order_total > orders|length %}
                    <div class="mt-6 flex justify-between items-center">
                        <p class="text-sm text-gray-500">Показване на 1-{{ orders|length }} от {{ order_total }} поръчки</p>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>
                                <i class="ri-arrow-left-s-line"></i>
                            </button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-white bg-primary hover:bg-primary/90">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">
                                <i class="ri-arrow-right-s-line"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-12">
                        <i class="ri-shopping-bag-line text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Нямате поръчки</h3>
                        <p class="text-gray-500 mb-6">Започнете да пазарувате, за да видите историята на поръчките си тук.</p>
                        <a href="{{ links.shop|default('index.php') }}" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors">
                            Започни пазаруване
                        </a>
                    </div>
                    {% endif %}
                </div>

                <!-- Favorites Section -->
                <div class="profile-section" id="favorites">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800">Любими продукти</h2>
                        <div class="flex space-x-4">
                            <div class="relative">
                                <select id="wishlistSortSelect" class="appearance-none px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 pr-8">
                                    <option value="">Сортиране по</option>
                                    <option value="newest">Най-нови</option>
                                    <option value="price-low">Цена (ниска към висока)</option>
                                    <option value="price-high">Цена (висока към ниска)</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <i class="ri-arrow-down-s-line text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if wishlist and wishlist|length > 0 %}
                    <div id="wishlistProductsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {% for product in wishlist %}
                        <a href="{{ ' ' }}{{ product.href }}" class="block group wishlist-product-card"
                           data-product-id="{{ ' ' }}{{ product.product_id }}"
                           data-product-options="{{ ' ' }}{{ product.options|default('{}')|json_encode|e('html_attr') }}"
                           data-product-info="{{ ' ' }}{{ product.product_info|default('{}')|json_encode|e('html_attr') }}">
                            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform transform hover:scale-105">
                                <div class="relative">
                                    <div class="h-64 overflow-hidden">
                                        <img src="{{ ' ' }}{{ product.image }}" alt="{{ ' ' }}{{ product.name }}" class="w-full h-full object-cover object-top transition-transform duration-300 group-hover:scale-110">
                                    </div>
                                    <button class="absolute top-2 right-2 w-8 h-8 flex items-center justify-center bg-white rounded-full shadow cursor-pointer hover:text-primary transition-colors wishlist-btn in-wishlist text-primary"
                                            data-product-id="{{ ' ' }}{{ product.product_id }}"
                                            data-wishlist-btn="true"
                                            title="Премахни от любими">
                                        <i class="ri-heart-fill ri-lg"></i>
                                    </button>
                                    {% if product.labels %}
                                        <div class="absolute top-2 left-2 space-y-1">
                                            {% for label in product.labels %}
                                                <span class="{{ ' ' }}{{ label.class }}">{{ ' ' }}{{ label.text }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="p-4">
                                    <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">{{ ' ' }}{{ product.name }}</h3>
                                    <div class="flex justify-between items-center">
                                        <div class="flex flex-col">
                                            {% if product.special is not null and product.special is not same as(false) %}
                                                <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.special_formatted }}</span>
                                                <span class="text-gray-400 line-through text-sm">{{ ' ' }}{{ product.price_formatted }}</span>
                                            {% else %}
                                                <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.price_formatted }}</span>
                                            {% endif %}
                                        </div>
                                        <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap buyButton">Купи</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% if wishlist_total > wishlist|length %}
                    <div class="mt-8 flex justify-center">
                        <button class="bg-white text-primary px-6 py-3 rounded-button font-medium border border-primary hover:bg-primary/5 transition-colors whitespace-nowrap" id="loadMoreWishlist"
                            data-page="2"
                            data-limit="8"
                            data-loaded="{{ wishlist|length }}"
                            data-total="{{ wishlist_total }}">
                            Зареди още продукти
                        </button>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-12">
                        <i class="ri-heart-line text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Нямате любими продукти</h3>
                        <p class="text-gray-500 mb-6">Започнете да добавяте продукти в списъка с любими, за да ги видите тук.</p>
                        <a href="{{ links.shop|default('index.php') }}" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors">
                            Разгледай продукти
                        </a>
                    </div>
                    {% endif %}
                </div>

                <!-- Settings Section -->
                <div class="profile-section" id="settings">
                    <h2 class="text-2xl font-semibold text-gray-800 mb-6">Настройки на профила</h2>
                    <div class="space-y-8">
                        <!-- Notification Settings -->
                        <div class="bg-white rounded-lg p-6 border border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4">Известия</h3>
                            <form id="notificationSettingsForm" class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-800">Промоции и специални оферти</p>
                                        <p class="text-sm text-gray-500">Получавайте информация за нови продукти и промоции</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" name="newsletter" class="sr-only peer" {% if customer.newsletter %}checked{% endif %}>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                                    </label>
                                </div>
                                <div class="pt-4">
                                    <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors shadow-md shadow-primary/20 whitespace-nowrap">
                                        Запази настройките
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Password Change -->
                        <div class="bg-white rounded-lg p-6 border border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4">Промяна на парола</h3>
                            <form id="changePasswordForm" class="space-y-4">
                                <div>
                                    <label for="current-password" class="block text-sm font-medium text-gray-700 mb-1">Текуща парола</label>
                                    <div class="relative">
                                        <input type="password" id="current-password" name="current_password" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="••••••••">
                                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 password-toggle">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="new-password" class="block text-sm font-medium text-gray-700 mb-1">Нова парола</label>
                                    <div class="relative">
                                        <input type="password" id="new-password" name="new_password" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="••••••••">
                                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 password-toggle">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">Потвърди нова парола</label>
                                    <div class="relative">
                                        <input type="password" id="confirm-password" name="confirm_password" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="••••••••">
                                        <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 password-toggle">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="pt-2">
                                    <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors shadow-md shadow-primary/20 whitespace-nowrap">
                                        Промени паролата
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Delete Account -->
                        <div class="bg-white rounded-lg p-6 border border-gray-200">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4">Изтриване на профил</h3>
                            <p class="text-gray-600 mb-4">Внимание: Изтриването на профила е необратимо действие. Всички ваши данни, история на поръчки и запазени адреси ще бъдат премахнати.</p>
                            <button class="text-red-600 border border-red-600 px-6 py-3 rounded-button font-medium hover:bg-red-50 transition-colors whitespace-nowrap" id="deleteAccountBtn">
                                Изтрий моя профил
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Modals -->
<div id="addressModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white w-full max-w-2xl rounded-lg p-6 shadow-xl">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-gray-800" id="addressModalTitle">Добави нов адрес</h3>
            <button class="text-gray-500 hover:text-gray-700" id="closeAddressModal">
                <i class="ri-close-line text-2xl"></i>
            </button>
        </div>
        <form id="addressForm" class="space-y-4">
            <input type="hidden" id="address_id" name="address_id" value="">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="address_firstname" class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                    <input type="text" id="address_firstname" name="firstname" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div>
                    <label for="address_lastname" class="block text-sm font-medium text-gray-700 mb-1">Фамилия</label>
                    <input type="text" id="address_lastname" name="lastname" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div class="col-span-2">
                    <label for="address_alias" class="block text-sm font-medium text-gray-700 mb-1">Име на адреса (по желание)</label>
                    <input type="text" id="address_alias" name="alias" placeholder="Например: Домашен, Офис, Вила и т.н." class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div class="col-span-2">
                    <label for="address_1" class="block text-sm font-medium text-gray-700 mb-1">Адрес</label>
                    <input type="text" id="address_1" name="address_1" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div class="col-span-2">
                    <label for="address_2" class="block text-sm font-medium text-gray-700 mb-1">Допълнение към адреса (по желание)</label>
                    <input type="text" id="address_2" name="address_2" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div>
                    <label for="address_city" class="block text-sm font-medium text-gray-700 mb-1">Град</label>
                    <input type="text" id="address_city" name="city" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
                <div>
                    <label for="address_postcode" class="block text-sm font-medium text-gray-700 mb-1">Пощенски код</label>
                    <input type="text" id="address_postcode" name="postcode" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                </div>
            </div>
            <div class="flex items-center mt-4">
                <label class="custom-checkbox">
                    <input type="checkbox" id="default" name="default">
                    <span class="checkmark">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M0 11l2-2 5 5L18 3l2 2L7 18z"/>
                        </svg>
                    </span>
                    <span class="text-sm text-gray-700">Използвай като основен адрес</span>
                </label>
            </div>
            <div class="flex justify-end space-x-4 mt-6">
                <button type="button" class="px-6 py-2 border border-gray-300 text-gray-600 rounded-button font-medium hover:bg-gray-50 transition-colors" id="cancelAddressBtn">Отказ</button>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-button font-medium hover:bg-opacity-90 transition-colors shadow-md shadow-primary/20" id="saveAddressBtn">Запази</button>
            </div>
        </form>
    </div>
</div>

<!-- Logout Confirmation Dialog -->
<div id="logoutConfirmModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white w-96 rounded-lg p-6 shadow-xl">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Потвърждение</h3>
        <p class="text-gray-600 mb-6">Сигурни ли сте, че искате да излезете от профила си?</p>
        <div class="flex justify-end space-x-4">
            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium" id="cancelLogoutBtn">Отказ</button>
            <button class="px-4 py-2 bg-primary text-white rounded-button font-medium hover:bg-opacity-90" id="confirmLogoutBtn">Изход</button>
        </div>
    </div>
</div>

<!-- Delete Address Confirmation Dialog -->
<div id="deleteConfirmModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white w-96 rounded-lg p-6 shadow-xl">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Потвърждение</h3>
        <p class="text-gray-600 mb-6">Сигурни ли сте, че искате да изтриете този адрес?</p>
        <div class="flex justify-end space-x-4">
            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium" id="cancelDeleteBtn">Отказ</button>
            <button class="px-4 py-2 bg-red-600 text-white rounded-button font-medium hover:bg-red-700" id="confirmDeleteBtn">Изтрий</button>
        </div>
    </div>
</div>

<!-- Delete Account Confirmation Dialog -->
<div id="deleteAccountModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="bg-white w-96 rounded-lg p-6 shadow-xl">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Изтриване на профил</h3>
        <p class="text-gray-600 mb-4">Моля, въведете паролата си за потвърждение на изтриването:</p>
        <form id="deleteAccountForm" class="space-y-4">
            <div>
                <input type="password" name="password" placeholder="Въведете парола" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" required>
            </div>
            <div class="flex justify-end space-x-4">
                <button type="button" class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium" id="cancelDeleteAccountBtn">Отказ</button>
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-button font-medium hover:bg-red-700">Изтрий профила</button>
            </div>

            <div id="deleteAccountError" class="text-red-600 mt-2 hidden"></div>
        </form>
    </div>
</div>

<!-- Success Notification -->
<div id="notification" class="fixed top-4 right-4 z-50 hidden bg-white rounded-lg shadow-lg overflow-hidden">
    <div class="flex p-4">
        <div class="flex-shrink-0" id="notificationIcon">
            <!-- Icons will be set dynamically -->
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium text-gray-900" id="notificationText"></p>
        </div>
        <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
                <button class="inline-flex rounded-md p-1.5 text-gray-400 hover:text-gray-500" id="closeNotification">
                    <i class="ri-close-line"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="h-1 bg-primary" id="notificationProgress" style="width: 100%"></div>
</div>
