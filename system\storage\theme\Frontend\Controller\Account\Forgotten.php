<?php

namespace Theme25\Frontend\Controller\Account;

class Forgotten extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'account/forgotten');
        $this->loadModelAs('account/customer', 'customerModel');
    }

    public function index() {
        // Ако потребителят вече е вписан, пренасочваме го
        if ($this->customer->isLogged()) {
            $this->response->redirect($this->getLink('account/account', '', true));
        }

        // Обработка на POST заявка от формата
        if ($this->isPostRequest()) {
            $this->processPasswordResetRequest();
            return; // processPasswordResetRequest ще прекрати изпълнението с JSON отговор
        }

        // Стандартно зареждане на страницата при GET заявка
        $this->setTitle('Забравена парола');
        $this->addFrontendScriptWithVersion('account.js', 'footer');

        $routes = [
            'action' => 'account/forgotten',
            'login'  => 'account/login',
            'register' => 'account/register',
            'home' => 'common/home'
        ];

        $data['links'] = $this->getLinks($routes);
        $data['success'] = $this->session->data['success'] ?? '';
        
        // Изчистваме съобщението за успех, след като е показано
        if (isset($this->session->data['success'])) {
            unset($this->session->data['success']);
        }
        
        $this->renderTemplateWithDataAndOutput('account/forgotten', $data);
    }
    
    /**
     * Обработва заявка за възстановяване на парола
     */
    private function processPasswordResetRequest() {
        $json = [];

        // Валидация на email адреса
        if (!$this->requestPost('email') || !filter_var($this->requestPost('email'), FILTER_VALIDATE_EMAIL)) {
            $json['error'] = 'Моля, въведете валиден имейл адрес';
            $this->jsonResponse($json);
            return;
        }
        
        // Проверка за валиден имейл
        if (!filter_var($this->requestPost('email'), FILTER_VALIDATE_EMAIL)) {
            $json['error'] = 'Моля, въведете валиден имейл адрес.';
            $this->jsonResponse($json);
            return;
        }
        
        // Проверка на reCAPTCHA
        // $recaptchaResponse = $this->requestPost('g-recaptcha-response');
        // if (!$this->validateRecaptcha($recaptchaResponse)) {
        //     $json['error'] = 'Моля, потвърдете че не сте робот.';
        //     $this->jsonResponse($json);
        //     return;
        // }

        // Проверка на броя опити за възстановяване на парола
        $this->loadModelAs('account/customer', 'customerModel');

        $customerModel = clone $this->customerModel;
        
        // Проверка дали няма прекалено много опити
        $loginAttempts = $customerModel->getLoginAttempts($this->requestPost('email'));

        if ($loginAttempts && $loginAttempts['total'] >= 3) {

            if($loginAttempts['date_modified'] < date('Y-m-d H:i:s', strtotime('-1 hour'))) {
                $customerModel->deleteLoginAttempts($this->requestPost('email'));
            }
            else {
                $json['error'] = 'Преминат е максималния брой опити за възстановяване на парола. Моля, опитайте отново след 1 час.';
                $this->jsonResponse($json);
                return;
            }
        }
        
        // Проверка дали имейла съществува в базата
        $customerInfo = $customerModel->getCustomerByEmail($this->requestPost('email'));
        
        // За сигурност, не разкриваме дали имейла съществува
        // Изпращаме имейл само ако клиентът съществува
        if ($customerInfo) {

            // Генерираме токен и записваме в базата
            $code = $this->generateRandomToken(40);

            if(CM()->shouldUseSecondDb('account/forgotten')) {
                $db = CM()->getSecondDatabase();
            } else {
                $db = $this->db;
            }

            $sql = "UPDATE `" . DB_PREFIX . "customer` SET code = '" . $this->db->escape($code) . "' WHERE LCASE(email) = '" . $this->db->escape(utf8_strtolower($this->requestPost('email'))) . "'";

            $db->query($sql);


            // Изпращаме имейл за възстановяване
            $subject = 'Възстановяване на парола за ' . $this->config->get('config_name');
            
            $data = [
                'firstname' => $customerInfo['firstname'],
                'lastname' => $customerInfo['lastname'],
                'customer_id' => $customerInfo['customer_id'],  // Добавяме ID на клиента за рендерирането
                'store_name' => $this->config->get('config_name'),
                'store_email' => $this->config->get('config_email'),
                'store_phone' => $this->config->get('config_telephone'),
                'ip' => $this->request->server['REMOTE_ADDR'],
                'reset_link' => $this->getLink('account/reset', 'code=' . $code, true),  // Подаваме true за пълен URL
            ];

            
            // Използваме шаблон за имейла
            $this->sendEmailWithTemplate($this->requestPost('email'), $subject, 'password_reset', $data);
        }
        
        
        // Записваме опита за възстановяване на парола
        $customerModel->addLoginAttempt($this->requestPost('email'));

        // Връщаме успешен отговор
        $json['success'] = true;
        $json['message'] = 'Изпратихме инструкции за възстановяване на паролата на имейл адреса <strong>' . $this->requestPost('email') . '</strong>. Моля, проверете пощата си.';
        
        $this->jsonResponse($json);
    }
    
    /**
     * Изпраща имейл с шаблон използвайки Renderer и Mailer класове за правилно рендиране
     * 
     * @param string $to Имейл адрес на получателя
     * @param string $subject Тема на имейла
     * @param string $templateCode Код на шаблона
     * @param array $data Данни за шаблона
     * @return bool Успешно ли е изпратен имейлът
     */
    private function sendEmailWithTemplate($to, $subject, $templateCode, $data) {
        try {
            // Получаваме customer ID от данните
            $customerId = !empty($data['customer_id']) ? (int)$data['customer_id'] : 0;
            
            // Използваме класа Renderer за рендиране на шаблона
            $renderer = new \Theme25\Notifications\Renderer();
            
            // Проверяваме дали шаблонът е валиден
            if (!$renderer->isValidTemplate($templateCode)) {
                $this->log->write('Грешка: Невалиден шаблон: ' . $templateCode);
                return false;
            }
            
            // Създаваме инстанция на Mailer класа за изпращане на имейл
            $mailer = new \Theme25\Notifications\Mailer();
            
            // Изпращаме имейла директно чрез метода sendTemplateEmail на Mailer класа
            // Този метод автоматично рендира шаблона и изпраща имейла
            $result = $mailer->sendTemplateEmail($customerId, $templateCode, $to, $subject, 'Backend', $data);

  
            // Логваме резултата от изпращането
            if ($result) {
                $this->log->write('Изпратен имейл за възстановяване на парола до: ' . $to);
                F()->log->developer('Изпратен имейл за възстановяване на парола до: ' . $to, __FILE__, __LINE__);
                return true;
            } else {
                $this->log->write('Грешка при изпращане на имейл до: ' . $to);
                F()->log->developer('Грешка при изпращане на имейл до: ' . $to, __FILE__, __LINE__);
                return false;
            }
        } catch (\Exception $e) {
            $this->log->write('Грешка при изпращане на имейл: ' . $e->getMessage());
            F()->log->developer('Грешка при изпращане на имейл: ' . $e->getMessage(), __FILE__, __LINE__);
            return false;
        }
    }
    
    /**
     * Генерира случаен токен с определена дължина
     * 
     * @param int $length Дължина на токена
     * @return string Случайно генериран токен
     */
    private function generateRandomToken($length = 40) {
        // Използваме вградения метод generateToken от базовия контролер
        if (is_callable([$this, 'generateToken'])) {
            return $this->generateToken($length);
        }
        
        // Алтернативна имплементация, ако методът не съществува
        $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charsLength = strlen($chars);
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $chars[mt_rand(0, $charsLength - 1)];
        }
        
        return $randomString;
    }
}
