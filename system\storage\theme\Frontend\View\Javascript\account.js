(function() {
    'use strict';

    // Create AccountModule by extending FrontendModule
    const AccountModule = Object.create(FrontendModule);

    // Override/extend configuration
    Object.assign(AccountModule, {
        init: function() {
            // Call parent init if needed
            // FrontendModule.init.call(this);
            if (document.getElementById('input-email') && document.querySelector('form[action*="account/login"]')) {
                this.initLoginPage();
            }
            if (document.getElementById('registerButton')) {
                this.initRegisterPage();
            }
            if (document.getElementById('resetButton')) {
                this.initForgottenPasswordPage();
            }
            if (document.querySelector('.profile-tab')) {
                this.initAccountPage();
            }

            // Инициализиране на wishlist функционалност
            this.initWishlistButtons();
        },

        initLoginPage: function() {
            // Custom Checkbox Functionality
            const checkbox = document.getElementById('remember');
            if (checkbox) {
                const checkboxDiv = checkbox.nextElementSibling;
                const checkmark = checkboxDiv.querySelector('svg');
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        checkmark.classList.remove('hidden');
                    } else {
                        checkmark.classList.add('hidden');
                    }
                });
            }

            // Password Toggle Functionality
            const passwordToggle = document.querySelector('.password-toggle');
            if (passwordToggle) {
                const passwordInput = document.getElementById('input-password');
                passwordToggle.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('ri-eye-line');
                        icon.classList.add('ri-eye-off-line');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('ri-eye-off-line');
                        icon.classList.add('ri-eye-line');
                    }
                });
            }

            // Form Validation
            const loginForm = document.querySelector('form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const email = document.getElementById('input-email').value;
                    const password = document.getElementById('input-password').value;
                    
                    // Simple validation
                    if (!email || !password) {
                        // Replace with a more elegant notification
                        AccountModule.showNotification('Моля, попълнете всички полета', 'warning');
                        return;
                    }

                    const submitButton = this.querySelector('button[type="submit"]');
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Обработване...';
                    submitButton.disabled = true;

                    const formData = new FormData(loginForm);

                    AccountModule.makeAjaxRequest(loginForm.action, formData,
                        (response) => {

                            console.log(response);

                            if (response.redirect) {
                                window.location.href = response.redirect;
                            } else {
                                // Handle success but no redirect (e.g. show message)
                                submitButton.innerHTML = 'Вход';
                                submitButton.disabled = false;
                            }
                        },
                        (error) => {
                            AccountModule.showNotification(error.message || 'Възникна грешка.', 'danger');
                            submitButton.innerHTML = 'Вход';
                            submitButton.disabled = false;
                        }
                    );
                });
            }
        },

        initRegisterPage: function() {
            const validations = {
                'first-name': value => value.length >= 2 && /^[A-Za-zА-Яа-я\s]+$/.test(value),
                'last-name': value => value.length >= 2 && /^[A-Za-zА-Яа-я\s]+$/.test(value),
                'phone': value => /^(\+359|0)[0-9]{9}$/.test(value),
                'email': value => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                'address': value => value.length >= 5,
                'city': value => value.length >= 2 && /^[A-Za-zА-Яа-я\s]+$/.test(value),
                'postal-code': value => /^\d{4}$/.test(value),
                'password': value => value.length >= 8, // Add more complex regex if needed
                'confirm-password': value => value === document.getElementById('password').value && value.length > 0
            };

            let formValidationState = {};

            const registerForm = document.querySelector('form[action*="account/register"]');
            if (!registerForm) return;

            const inputs = registerForm.querySelectorAll('.form-input');
            const registerButton = document.getElementById('registerButton');

            inputs.forEach(input => {
                formValidationState[input.id] = false;
                const parentDiv = input.parentElement;
                if (!parentDiv.querySelector('.validation-icon')) {
                     if (input.type !== 'password') { // Password fields have them already
                        const validationIcon = document.createElement('div');
                        validationIcon.className = 'absolute inset-y-0 right-0 pr-3 flex items-center hidden validation-icon';
                        validationIcon.innerHTML = `
                            <i class="ri-checkbox-circle-line text-green-500 hidden valid-icon"></i>
                            <i class="ri-close-circle-line text-red-500 hidden invalid-icon"></i>
                        `;
                        parentDiv.appendChild(validationIcon);
                    }
                }

                input.addEventListener('input', () => this.validateField(input, validations, formValidationState));
                input.addEventListener('blur', () => this.validateField(input, validations, formValidationState));
            });
            
            const termsCheckbox = document.getElementById('terms');
            if (termsCheckbox) {
                 formValidationState['terms'] = false;
                 termsCheckbox.addEventListener('change', () => {
                    formValidationState['terms'] = termsCheckbox.checked;
                    this.updateSubmitButton(registerButton, formValidationState);
                });
            }

            const passwordToggles = document.querySelectorAll('.password-toggle');
            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const passwordInput = this.closest('.relative').querySelector('input');
                    const icon = this.querySelector('i');
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('ri-eye-line');
                        icon.classList.add('ri-eye-off-line');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('ri-eye-off-line');
                        icon.classList.add('ri-eye-line');
                    }
                });
            });

            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const isFormValid = Object.values(formValidationState).every(value => value === true);
                if (!isFormValid) {
                    this.showNotification('Моля, попълнете коректно всички задължителни полета.', 'warning');
                    return;
                }

                registerButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Обработване...';
                registerButton.disabled = true;

                const formData = new FormData(registerForm);

                this.makeAjaxRequest(registerForm.action, formData,
                    (response) => {

                        console.log(response);
                        
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        } else {
                            registerButton.innerHTML = 'Регистрация';
                            registerButton.disabled = false;
                        }
                    },
                    (error) => {
                        this.showNotification(error.message || 'Възникна грешка при регистрацията.', 'danger');
                        registerButton.innerHTML = 'Регистрация';
                        registerButton.disabled = false;
                    }
                );
            });
        },

        validateField: function(input, validations, formValidationState) {
            const validationFn = validations[input.id];
            if (!validationFn) return;

            const isValid = validationFn(input.value);
            formValidationState[input.id] = isValid;

            const validationIconContainer = input.parentElement.querySelector('.validation-icon');
            if (!validationIconContainer) return;

            const validIcon = validationIconContainer.querySelector('.valid-icon');
            const invalidIcon = validationIconContainer.querySelector('.invalid-icon');
            const mismatchMessage = input.id === 'confirm-password' ? document.querySelector('.password-mismatch-message') : null;

            if (input.value.length > 0) {
                validationIconContainer.classList.remove('hidden');
                if (isValid) {
                    validIcon.classList.remove('hidden');
                    invalidIcon.classList.add('hidden');
                    input.classList.remove('border-red-500');
                    input.classList.add('border-green-500');
                    if (mismatchMessage) mismatchMessage.classList.add('hidden');
                } else {
                    validIcon.classList.add('hidden');
                    invalidIcon.classList.remove('hidden');
                    input.classList.remove('border-green-500');
                    input.classList.add('border-red-500');
                    if (mismatchMessage && input.id === 'confirm-password') {
                        mismatchMessage.classList.remove('hidden');
                    }
                }
            } else {
                validationIconContainer.classList.add('hidden');
                input.classList.remove('border-green-500', 'border-red-500');
                if (mismatchMessage) mismatchMessage.classList.add('hidden');
            }

            if (input.id === 'password') {
                const confirmPasswordInput = document.getElementById('confirm-password');
                if (confirmPasswordInput.value.length > 0) {
                    this.validateField(confirmPasswordInput, validations, formValidationState);
                }
            }
            
            const registerButton = document.getElementById('registerButton');
            this.updateSubmitButton(registerButton, formValidationState);
        },

        updateSubmitButton: function(button, formValidationState) {
            if (!button) return;
            const isFormValid = Object.values(formValidationState).every(value => value === true);

            if (isFormValid) {
                button.disabled = false;
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                button.classList.add('hover:bg-opacity-90');
            } else {
                button.disabled = true;
                button.classList.add('opacity-50', 'cursor-not-allowed');
                button.classList.remove('hover:bg-opacity-90');
            }
        },

        initAccountPage: function() {
            // Управление на табовете
            this.initProfileTabs();

            // Инициализация на подстраниците
            this.initPersonalInfo();
            this.initAddresses();
            this.initOrders();
            this.initWishlist();
            this.initSettings();
            this.initLogout();
            
            // Инициализация на уведомления
            this.initNotifications();

            // Инициализация на password toggle функционалност
            this.initPasswordToggles();
        },
        
        initProfileTabs: function() {
            const profileTabs = document.querySelectorAll('.profile-tab');
            const profileSections = document.querySelectorAll('.profile-section');
            
            profileTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.getAttribute('data-tab');
                    // Remove active class from all tabs and sections
                    profileTabs.forEach(t => t.classList.remove('active'));
                    profileSections.forEach(s => s.classList.remove('active'));
                    // Add active class to clicked tab and corresponding section
                    tab.classList.add('active');
                    const section = document.getElementById(tabId);
                    if (section) {
                        section.classList.add('active');
                    }
                });
            });
        },
        
        initPersonalInfo: function() {
            const personalInfoForm = document.getElementById('personalInfoForm');
            if (!personalInfoForm) return;
            
            personalInfoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                // Валидация на формата
                const formData = new FormData(personalInfoForm);
                const submitButton = personalInfoForm.querySelector('button[type="submit"]');
                
                // Показване на зареждане
                submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Запазване...';
                submitButton.disabled = true;
                
                // AJAX заявка за обновяване на личната информация
                this.makeAjaxRequest('index.php?route=account/account/updateInfo', formData,
                    (response) => {
                        submitButton.innerHTML = 'Запази промените';
                        submitButton.disabled = false;
                        
                        if (response.success) {
                            this.showNotification(response.success, 'success');
                        } else if (response.error) {
                            this.showNotification(response.error, 'error');
                        }
                    },
                    (error) => {
                        submitButton.innerHTML = 'Запази промените';
                        submitButton.disabled = false;
                        this.showNotification('Възникна грешка при обновяване на информацията.', 'error');
                    }
                );
            });
        },
        
        initAddresses: function() {
            // Показване на модалния прозорец за добавяне на нов адрес
            const addAddressBtn = document.getElementById('addAddressBtn');
            const noAddressesBtn = document.getElementById('noAddressesBtn');
            const addressModal = document.getElementById('addressModal');
            const closeAddressModal = document.getElementById('closeAddressModal');
            const cancelAddressBtn = document.getElementById('cancelAddressBtn');
            const addressForm = document.getElementById('addressForm');
            const addressModalTitle = document.getElementById('addressModalTitle');
            
            // Модални прозорци за потвърждение при изтриване
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            let currentAddressId = null;
            
            if (addAddressBtn) {
                addAddressBtn.addEventListener('click', () => {
                    this.showAddAddressModal();
                });
            }
            
            if (noAddressesBtn) {
                noAddressesBtn.addEventListener('click', () => {
                    this.showAddAddressModal();
                });
            }
            
            if (closeAddressModal) {
                closeAddressModal.addEventListener('click', () => {
                    this.hideAddressModal();
                });
            }
            
            if (cancelAddressBtn) {
                cancelAddressBtn.addEventListener('click', () => {
                    this.hideAddressModal();
                });
            }
            
            // Управление на радио бутоните за основен адрес
            const defaultAddressRadios = document.querySelectorAll('.default-address-radio');
            defaultAddressRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    if (radio.checked) {
                        this.setDefaultAddress(radio.value);
                    }
                });
            });
            
            // Обработка на бутони за редактиране на адрес
            const editButtons = document.querySelectorAll('.edit-address-btn');
            editButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const addressItem = button.closest('[data-address-id]');
                    const addressId = addressItem.getAttribute('data-address-id');
                    this.editAddress(addressId);
                });
            });
            
            // Обработка на бутони за изтриване на адрес
            const deleteButtons = document.querySelectorAll('.delete-address-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const addressItem = button.closest('[data-address-id]');
                    const addressId = addressItem.getAttribute('data-address-id');
                    currentAddressId = addressId;
                    deleteConfirmModal.classList.remove('hidden');
                    deleteConfirmModal.classList.add('flex');
                });
            });
            
            if (cancelDeleteBtn) {
                cancelDeleteBtn.addEventListener('click', () => {
                    deleteConfirmModal.classList.add('hidden');
                    deleteConfirmModal.classList.remove('flex');
                });
            }
            
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', () => {
                    this.deleteAddress(currentAddressId);
                    deleteConfirmModal.classList.add('hidden');
                    deleteConfirmModal.classList.remove('flex');
                });
            }
            
            // Обработка на формата за добавяне/редактиране на адрес
            if (addressForm) {
                addressForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    
                    // Получаваме данните от формата
                    const formData = new FormData(addressForm);
                    const addressId = formData.get('address_id');
                    const saveButton = document.getElementById('saveAddressBtn');
                    
                    // Показваме зареждане
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Запазване...';
                    saveButton.disabled = true;
                    
                    // Определяме дали добавяме или редактираме адрес
                    const url = addressId 
                        ? 'index.php?route=account/account/editAddress' 
                        : 'index.php?route=account/account/addAddress';
                    
                    // AJAX заявка
                    this.makeAjaxRequest(url, formData,
                        (response) => {
                            saveButton.innerHTML = 'Запази';
                            saveButton.disabled = false;
                            
                            if (response.success) {
                                this.showNotification(response.success, 'success');
                                this.hideAddressModal();
                                
                                // Презареждаме страницата, за да видим промените
                                // В реална ситуация би било по-добре да обновим DOM-а директно
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else if (response.error) {
                                this.showNotification(typeof response.error === 'object' ? 'Моля, попълнете всички задължителни полета.' : response.error, 'error');
                            }
                        },
                        (error) => {
                            saveButton.innerHTML = 'Запази';
                            saveButton.disabled = false;
                            this.showNotification('Възникна грешка при запазване на адреса.', 'error');
                        }
                    );
                });
            }
        },
        
        initLogout: function() {
            const logoutButton = document.getElementById('logoutButton');
            const logoutConfirmModal = document.getElementById('logoutConfirmModal');
            const cancelLogoutBtn = document.getElementById('cancelLogoutBtn');
            const confirmLogoutBtn = document.getElementById('confirmLogoutBtn');
            
            if (logoutButton) {
                logoutButton.addEventListener('click', () => {
                    logoutConfirmModal.classList.remove('hidden');
                    logoutConfirmModal.classList.add('flex');
                });
            }
            
            if (cancelLogoutBtn) {
                cancelLogoutBtn.addEventListener('click', () => {
                    logoutConfirmModal.classList.add('hidden');
                    logoutConfirmModal.classList.remove('flex');
                });
            }
            
            if (confirmLogoutBtn) {
                confirmLogoutBtn.addEventListener('click', () => {
                    window.location.href = 'index.php?route=account/logout';
                });
            }
        },
        
        initNotifications: function() {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notificationText');
            const notificationIcon = document.getElementById('notificationIcon');
            const closeNotification = document.getElementById('closeNotification');
            const notificationProgress = document.getElementById('notificationProgress');
            
            if (closeNotification) {
                closeNotification.addEventListener('click', () => {
                    notification.classList.add('hidden');
                });
            }
        },
        
        showNotification: function(message, type = 'success') {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notificationText');
            const notificationIcon = document.getElementById('notificationIcon');
            const notificationProgress = document.getElementById('notificationProgress');
            
            if (!notification || !notificationText || !notificationIcon) return;
            
            // Задаваме текста на съобщението
            notificationText.textContent = message;
            
            // Задаваме иконата според типа на съобщението
            switch (type) {
                case 'success':
                    notificationIcon.innerHTML = '<i class="ri-checkbox-circle-line text-green-500 text-xl"></i>';
                    notificationProgress.className = 'h-1 bg-green-500';
                    break;
                case 'error':
                    notificationIcon.innerHTML = '<i class="ri-error-warning-line text-red-500 text-xl"></i>';
                    notificationProgress.className = 'h-1 bg-red-500';
                    break;
                case 'warning':
                    notificationIcon.innerHTML = '<i class="ri-alert-line text-yellow-500 text-xl"></i>';
                    notificationProgress.className = 'h-1 bg-yellow-500';
                    break;
                case 'info':
                    notificationIcon.innerHTML = '<i class="ri-information-line text-blue-500 text-xl"></i>';
                    notificationProgress.className = 'h-1 bg-blue-500';
                    break;
            }
            
            // Показваме съобщението
            notification.classList.remove('hidden');
            notification.classList.add('flex');
            
            // Стартираме анимацията на лентата за прогрес
            notificationProgress.style.transition = 'width 3s linear';
            setTimeout(() => {
                notificationProgress.style.width = '0%';
            }, 100);
            
            // Скриваме съобщението след 3 секунди
            setTimeout(() => {
                notification.classList.add('hidden');
                notification.classList.remove('flex');
                notificationProgress.style.width = '100%';
                notificationProgress.style.transition = 'none';
            }, 3100);
        },
        
        showAddAddressModal: function() {
            const addressModal = document.getElementById('addressModal');
            const addressForm = document.getElementById('addressForm');
            const addressModalTitle = document.getElementById('addressModalTitle');
            
            // Изчистваме формата
            addressForm.reset();
            addressForm.querySelector('#address_id').value = '';
            
            // Задаваме заглавието
            addressModalTitle.textContent = 'Добави нов адрес';
            
            // Показваме модалния прозорец
            addressModal.classList.remove('hidden');
            addressModal.classList.add('flex');
        },
        
        hideAddressModal: function() {
            const addressModal = document.getElementById('addressModal');
            addressModal.classList.add('hidden');
            addressModal.classList.remove('flex');
        },
        
        editAddress: function(addressId) {
            // Показваме модалния прозорец с данните за адреса
            const addressModal = document.getElementById('addressModal');
            const addressForm = document.getElementById('addressForm');
            const addressModalTitle = document.getElementById('addressModalTitle');
            
            // Задаваме заглавието
            addressModalTitle.textContent = 'Редактирай адрес';
            
            // Показваме зареждане
            addressModalTitle.innerHTML = 'Редактирай адрес <i class="ri-loader-4-line animate-spin ml-2"></i>';
            
            // AJAX заявка за получаване на данните за адреса
            const formData = new FormData();
            formData.append('address_id', addressId);
            this.makeAjaxRequest('index.php?route=account/account/addressInfo', formData,
                (response) => {
                    if (response.address) {
                        // Попълваме формата с данните за адреса
                        addressForm.querySelector('#address_id').value = addressId;
                        addressForm.querySelector('#address_firstname').value = response.address.firstname || '';
                        addressForm.querySelector('#address_lastname').value = response.address.lastname || '';
                        addressForm.querySelector('#address_alias').value = response.address.alias || '';
                        addressForm.querySelector('#address_1').value = response.address.address_1 || '';
                        addressForm.querySelector('#address_2').value = response.address.address_2 || '';
                        addressForm.querySelector('#address_city').value = response.address.city || '';
                        addressForm.querySelector('#address_postcode').value = response.address.postcode || '';
                        
                        // Задаваме заглавието
                        addressModalTitle.textContent = 'Редактирай адрес';
                    } else {
                        // Не сме получили данни за адреса
                        addressModalTitle.textContent = 'Грешка при зареждане на адреса';
                    }
                },
                (error) => {
                    addressModalTitle.textContent = 'Грешка при зареждане на адреса';
                }
            );
            
            // Показваме модалния прозорец
            addressModal.classList.remove('hidden');
            addressModal.classList.add('flex');
        },
        
        deleteAddress: function(addressId) {
            // AJAX заявка за изтриване на адрес
            const formData = new FormData();
            formData.append('address_id', addressId);
            
            this.makeAjaxRequest('index.php?route=account/account/deleteAddress', formData,
                (response) => {
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                        
                        // Премахваме адреса от DOM-а
                        const addressItem = document.querySelector(`[data-address-id="${addressId}"]`);
                        if (addressItem) {
                            addressItem.remove();
                        }
                    } else if (response.error) {
                        this.showNotification(response.error, 'error');
                    }
                },
                (error) => {
                    this.showNotification('Възникна грешка при изтриване на адреса.', 'error');
                }
            );
        },
        
        setDefaultAddress: function(addressId) {
            // AJAX заявка за задаване на адрес като основен
            const formData = new FormData();
            formData.append('address_id', addressId);
            
            this.makeAjaxRequest('index.php?route=account/account/setDefaultAddress', formData,
                (response) => {
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                    } else if (response.error) {
                        this.showNotification(response.error, 'error');
                    }
                },
                (error) => {
                    this.showNotification('Възникна грешка при задаване на основен адрес.', 'error');
                }
            );
        },
        
        initOrders: function() {
            // Функционалност за търсене в поръчки
            const searchInput = document.querySelector('#orders input[placeholder*="Търсене"]');
            const statusFilter = document.querySelector('#orders select');
            
            if (searchInput) {
                searchInput.addEventListener('input', this.debounce(() => {
                    this.filterOrders();
                }, 300));
            }
            
            if (statusFilter) {
                statusFilter.addEventListener('change', () => {
                    this.filterOrders();
                });
            }
        },
        
        initWishlist: function() {
            // Инициализираме wishlist бутоните с новата структура
            this.initWishlistButtons();

            // Инициализираме сортирането
            this.initWishlistSorting();

            // Зареждане на още продукти
            const loadMoreBtn = document.getElementById('loadMoreWishlist');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', () => {
                    this.loadMoreWishlistProducts();
                });
            }
        },
        
        initSettings: function() {
            // Настройки за уведомления
            const notificationForm = document.getElementById('notificationSettingsForm');
            if (notificationForm) {
                notificationForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateNotificationSettings(notificationForm);
                });
            }
            
            // Промяна на парола
            const passwordForm = document.getElementById('changePasswordForm');
            if (passwordForm) {
                passwordForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.changePassword(passwordForm);
                });
            }
            
            // Предпочитания за комуникация
            const communicationForm = document.getElementById('communicationForm');
            if (communicationForm) {
                communicationForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updateCommunicationPreferences(communicationForm);
                });
            }
            
            // Изтриване на профил
            const deleteAccountBtn = document.getElementById('deleteAccountBtn');
            const deleteAccountModal = document.getElementById('deleteAccountModal');
            const cancelDeleteAccountBtn = document.getElementById('cancelDeleteAccountBtn');
            const deleteAccountForm = document.getElementById('deleteAccountForm');
            
            if (deleteAccountBtn) {
                deleteAccountBtn.addEventListener('click', () => {
                    deleteAccountModal.classList.remove('hidden');
                    deleteAccountModal.classList.add('flex');
                });
            }
            
            if (cancelDeleteAccountBtn) {
                cancelDeleteAccountBtn.addEventListener('click', () => {
                    deleteAccountModal.classList.add('hidden');
                    deleteAccountModal.classList.remove('flex');
                });
            }
            
            if (deleteAccountForm) {
                deleteAccountForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.deleteAccount(deleteAccountForm);
                });
            }
        },
        
        initPasswordToggles: function() {
            const passwordToggles = document.querySelectorAll('.password-toggle');
            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const passwordInput = this.closest('.relative').querySelector('input[type="password"], input[type="text"]');
                    const icon = this.querySelector('i');
                    
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('ri-eye-line');
                        icon.classList.add('ri-eye-off-line');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('ri-eye-off-line');
                        icon.classList.add('ri-eye-line');
                    }
                });
            });
        },
        
        updateNotificationSettings: function(form) {
            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            
            submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Запазване...';
            submitButton.disabled = true;
            
            this.makeAjaxRequest('index.php?route=account/account/updateNotifications', formData,
                (response) => {
                    submitButton.innerHTML = 'Запази настройките';
                    submitButton.disabled = false;
                    
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                    } else if (response.error) {
                        this.showNotification(response.error, 'error');
                    }
                },
                (error) => {
                    submitButton.innerHTML = 'Запази настройките';
                    submitButton.disabled = false;
                    this.showNotification('Възникна грешка при запазване на настройките.', 'error');
                }
            );
        },
        
        changePassword: function(form) {
            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            
            // Валидация на паролите
            const newPassword = formData.get('new_password');
            const confirmPassword = formData.get('confirm_password');
            
            if (newPassword !== confirmPassword) {
                this.showNotification('Паролата за потвърждение не съвпада с новата парола!', 'error');
                return;
            }
            
            submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Променяне...';
            submitButton.disabled = true;
            
            this.makeAjaxRequest('index.php?route=account/account/changePassword', formData,
                (response) => {
                    submitButton.innerHTML = 'Промени паролата';
                    submitButton.disabled = false;
                    
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                        form.reset();
                    } else if (response.error) {
                        this.showNotification(typeof response.error === 'object' ? 'Моля, проверете въведените данни.' : response.error, 'error');
                    }
                },
                (error) => {
                    submitButton.innerHTML = 'Промени паролата';
                    submitButton.disabled = false;
                    this.showNotification('Възникна грешка при промяна на паролата.', 'error');
                }
            );
        },
        
        updateCommunicationPreferences: function(form) {
            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            
            submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Запазване...';
            submitButton.disabled = true;
            
            this.makeAjaxRequest('index.php?route=account/account/updateNotifications', formData,
                (response) => {
                    submitButton.innerHTML = 'Запази предпочитанията';
                    submitButton.disabled = false;
                    
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                    } else if (response.error) {
                        this.showNotification(response.error, 'error');
                    }
                },
                (error) => {
                    submitButton.innerHTML = 'Запази предпочитанията';
                    submitButton.disabled = false;
                    this.showNotification('Възникна грешка при запазване на предпочитанията.', 'error');
                }
            );
        },
        
        deleteAccount: function(form) {
            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            
            submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Изтриване...';
            submitButton.disabled = true;
            
            this.makeAjaxRequest('index.php?route=account/account/deleteAccount', formData,
                (response) => {
                    if (response.success) {
                        this.showNotification(response.success, 'success');
                        
                        setTimeout(() => {
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            } else {
                                window.location.href = 'index.php?route=account/login';
                            }
                        }, 2000);
                    } else if (response.error) {
                        submitButton.innerHTML = 'Изтрий профила';
                        submitButton.disabled = false;
                        this.showNotification(response.error, 'error');
                    }
                },
                (error) => {
                    submitButton.innerHTML = 'Изтрий профила';
                    submitButton.disabled = false;
                    this.showNotification('Възникна грешка при изтриване на профила.', 'error');
                }
            );
        },
        
        removeFromWishlistFromAccount: function(productId, buttonElement) {
            // Показваме loading състояние
            this.setButtonLoadingState(buttonElement, true);

            const formData = new FormData();
            formData.append('product_id', productId);

            this.makeAjaxRequest('index.php?route=account/wishlist/remove', formData,
                (response) => {
                    // Спираме loading състоянието
                    this.setButtonLoadingState(buttonElement, false);

                    if (response.success) {
                        this.showNotification(response.message || 'Продуктът е премахнат от списъка с любими.', 'success');

                        // Актуализираме брояча на wishlist
                        if (response.wishlist_count !== undefined) {
                            this.updateWishlistCounter(response.wishlist_count);
                        }

                        // Премахваме продуктовата карта с плавна анимация
                        this.removeWishlistProductCard(buttonElement);

                    } else {
                        this.showNotification(response.error || 'Възникна грешка при премахване от списъка с любими.', 'error');
                    }
                },
                (error) => {
                    // Спираме loading състоянието
                    this.setButtonLoadingState(buttonElement, false);
                    this.showNotification('Възникна грешка при премахване от списъка с любими.', 'error');
                }
            );
        },

        /**
         * Премахва продуктовата карта от DOM-а с плавна анимация
         */
        removeWishlistProductCard: function(buttonElement) {
            // Намираме продуктовата карта (новата структура използва .wishlist-product-card)
            const productCard = buttonElement.closest('.wishlist-product-card') || buttonElement.closest('.product-card');

            if (productCard) {
                // Добавяме CSS клас за анимация на изчезване
                productCard.style.transition = 'all 0.3s ease-out';
                productCard.style.transform = 'scale(0.95)';
                productCard.style.opacity = '0';

                // След анимацията премахваме елемента от DOM-а
                setTimeout(() => {
                    productCard.remove();

                    // Проверяваме дали има останали продукти
                    this.checkEmptyWishlist();
                }, 300);
            }
        },

        /**
         * Проверява дали wishlist-ът е празен и показва съответното съобщение
         */
        checkEmptyWishlist: function() {
            const wishlistContainer = document.querySelector('#wishlistProductsGrid');
            const remainingProducts = wishlistContainer ? wishlistContainer.querySelectorAll('.wishlist-product-card, .product-card') : [];

            if (remainingProducts.length === 0) {
                // Показваме съобщение за празен wishlist
                const emptyMessage = `
                    <div class="text-center py-12">
                        <i class="ri-heart-line text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Нямате любими продукти</h3>
                        <p class="text-gray-600 mb-6">Добавете продукти в списъка си с любими, за да ги намирате по-лесно.</p>
                        <a href="/" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors">
                            Разгледайте продуктите
                        </a>
                    </div>
                `;

                if (wishlistContainer) {
                    wishlistContainer.outerHTML = emptyMessage;
                }
            }
        },

        /**
         * Инициализира сортирането на wishlist продуктите
         */
        initWishlistSorting: function() {
            const sortSelect = document.getElementById('wishlistSortSelect');
            if (sortSelect) {
                sortSelect.addEventListener('change', (e) => {
                    this.sortWishlistProducts(e.target.value);
                });
            }
        },

        /**
         * Сортира wishlist продуктите според избраната опция
         */
        sortWishlistProducts: function(sortType) {
            const wishlistGrid = document.getElementById('wishlistProductsGrid');
            if (!wishlistGrid) return;

            const productCards = Array.from(wishlistGrid.querySelectorAll('.wishlist-product-card'));
            if (productCards.length === 0) return;

            // Сортиране според избраната опция
            productCards.sort((a, b) => {
                switch (sortType) {
                    case 'newest':
                        // Сортиране по дата на добавяне (най-нови първо)
                        // Използваме data-product-id като fallback за сортиране
                        const idA = parseInt(a.getAttribute('data-product-id')) || 0;
                        const idB = parseInt(b.getAttribute('data-product-id')) || 0;
                        return idB - idA; // По-високи ID-та първо (по-нови)

                    case 'price-low':
                        // Сортиране по цена (ниска към висока)
                        return this.getProductPrice(a) - this.getProductPrice(b);

                    case 'price-high':
                        // Сортиране по цена (висока към ниска)
                        return this.getProductPrice(b) - this.getProductPrice(a);

                    default:
                        return 0; // Без сортиране
                }
            });

            // Пренареждане на елементите в DOM-а
            productCards.forEach(card => {
                wishlistGrid.appendChild(card);
            });
        },

        /**
         * Извлича цената на продукт от неговата карта
         */
        getProductPrice: function(productCard) {
            // Търсим цената в текста на елемента
            const priceElement = productCard.querySelector('.text-primary.font-bold.text-xl');
            if (priceElement) {
                const priceText = priceElement.textContent.trim();
                // Извличаме числото от текста (напр. "100.00 лв." -> 100.00)
                const priceMatch = priceText.match(/[\d,]+\.?\d*/);
                if (priceMatch) {
                    return parseFloat(priceMatch[0].replace(',', ''));
                }
            }
            return 0; // Fallback цена
        },
        
        loadMoreWishlistProducts: function() {
            // Реализация за зареждане на още продукти от wishlist чрез AJAX
            const loadMoreBtn = document.getElementById('loadMoreWishlist');
            if (!loadMoreBtn) return;
            
            // Показваме индикатор за зареждане
            loadMoreBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Зареждане...';
            loadMoreBtn.disabled = true;
            
            // Получаваме данните от бутона
            const page = parseInt(loadMoreBtn.dataset.page || '2');
            const limit = parseInt(loadMoreBtn.dataset.limit || '8');
            const loaded = parseInt(loadMoreBtn.dataset.loaded || '0');
            const total = parseInt(loadMoreBtn.dataset.total || '0');
            const sort = document.getElementById('wishlistSortSelect')?.value || 'newest';
            
            // Подготвяме данните за AJAX заявката
            const formData = new FormData();
            formData.append('page', page);
            formData.append('limit', limit);
            formData.append('sort', sort);
            
            // Изпращаме AJAX заявка
            this.makeAjaxRequest('index.php?route=account/wishlist/loadMore', formData,
                (response) => {
                    if (response.success && response.products && response.products.length > 0) {
                        // Добавяме новите продукти към списъка
                        const productsContainer = document.getElementById('wishlistProductsGrid');
                        if (productsContainer) {
                            // Добавяме всеки продукт към контейнера
                            response.products.forEach(product => {
                                productsContainer.appendChild(this.createWishlistProductCard(product));
                            });
                            
                            // Актуализираме бутона и данните в него
                            const newPage = page + 1;
                            const newLoaded = loaded + response.products.length;
                            
                            loadMoreBtn.dataset.page = newPage;
                            loadMoreBtn.dataset.loaded = newLoaded;
                            loadMoreBtn.innerHTML = 'Зареди още продукти';
                            loadMoreBtn.disabled = false;
                            
                            // Ако всички продукти са заредени, скриваме бутона
                            if (newLoaded >= total || !response.has_more) {
                                loadMoreBtn.style.display = 'none';
                                this.showNotification('Всички продукти са заредени', 'success');
                            }
                            
                            // Инициализираме wishlist бутоните за новите продукти
                            this.initWishlistButtons();
                        }
                    } else {
                        // Няма повече продукти или възникна грешка
                        loadMoreBtn.style.display = 'none';
                        if (response.error) {
                            this.showNotification(response.error, 'error');
                        } else {
                            this.showNotification('Няма повече продукти за зареждане', 'info');
                        }
                    }
                },
                (error) => {
                    // Възстановяваме бутона в случай на грешка
                    loadMoreBtn.innerHTML = 'Зареди още продукти';
                    loadMoreBtn.disabled = false;
                    this.showNotification('Възникна грешка при зареждане на продуктите', 'error');
                    console.error('Error loading more wishlist products:', error);
                }
            );
        },
        
        filterOrders: function() {
            const searchTerm = document.querySelector('#orders input[placeholder*="Търсене"]')?.value.toLowerCase() || '';
            const statusFilter = document.querySelector('#orders select')?.value || '';
            const orderRows = document.querySelectorAll('.order-row');
            
            orderRows.forEach(row => {
                const orderNumber = row.querySelector('.text-sm.font-medium').textContent.toLowerCase();
                const statusElement = row.querySelector('.px-3.py-1');
                const orderStatus = statusElement ? statusElement.textContent.toLowerCase() : '';
                
                const matchesSearch = orderNumber.includes(searchTerm);
                const matchesStatus = !statusFilter || orderStatus.includes(statusFilter.toLowerCase());
                
                if (matchesSearch && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        },
        
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
            
        initForgottenPasswordPage: function() {
            const emailInput = document.getElementById('email');
            if (!emailInput) return;
            
            const validationIconContainer = emailInput.parentElement.querySelector('.validation-icon');
            if (!validationIconContainer) return;
            
            const validIcon = validationIconContainer.querySelector('.valid-icon');
            const invalidIcon = validationIconContainer.querySelector('.invalid-icon');

            emailInput.addEventListener('input', function() {
                const value = this.value.trim();
                const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);

                if (value.length > 0) {
                    validationIconContainer.classList.remove('hidden');
                    if (isEmail) {
                        validIcon.classList.remove('hidden');
                        invalidIcon.classList.add('hidden');
                        this.classList.remove('border-red-500');
                        this.classList.add('border-green-500');
                    } else {
                        validIcon.classList.add('hidden');
                        invalidIcon.classList.remove('hidden');
                        this.classList.remove('border-green-500');
                        this.classList.add('border-red-500');
                    }
                } else {
                    validationIconContainer.classList.add('hidden');
                    this.classList.remove('border-green-500', 'border-red-500');
                }
            });

            const resetForm = document.getElementById('forgotten-form');
            if (!resetForm) return;
            
            resetForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const email = emailInput.value.trim();
                const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

                if (!isEmail) {
                    this.showNotification('Моля, въведете валиден имейл адрес.', 'warning');
                    return;
                }

                const submitButton = document.getElementById('resetButton');
                submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Обработване...';
                submitButton.disabled = true;

                const formData = new FormData(resetForm);

                this.makeAjaxRequest(resetForm.action, formData,
                    (response) => {
                        // Проверка за успешна заявка и обработка на отговора
                        console.log('Forgotten password response:', response);
                        
                        submitButton.innerHTML = 'Изпрати';
                        submitButton.disabled = false;

                        if (response.success) {
                            // Показване на съобщение за успешно изпращане
                            this.showNotification(response.message || 'Изпратихме инструкции на посочения имейл адрес.', 'success');
                            
                            // Показване на успешното съобщение в контейнера под формата
                            const responseContainer = document.getElementById('forgotten-response');
                            if (responseContainer) {
                                responseContainer.innerHTML = `
                                    <div class="bg-green-50 border-l-4 border-green-500 p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="ri-mail-send-line text-green-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-green-700">
                                                    ${response.message || 'Изпратихме инструкции за възстановяване на паролата на посочения имейл адрес.'}
                                                </p>
                                                <p class="mt-2 text-xs text-green-700">
                                                    Не получихте имейл? Проверете папката "Спам" или опитайте отново след 15 минути.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                responseContainer.classList.remove('hidden');
                            }
                            
                            // Ресетваме формата и полето за имейл
                            resetForm.reset();
                            emailInput.classList.remove('border-green-500', 'border-red-500');
                            
                            // Скриваме иконите за валидация
                            if (validationIconContainer) {
                                validationIconContainer.classList.add('hidden');
                            }
                        } else if (response.redirect) {
                            // Ако сървърът връща редирект
                            window.location.href = response.redirect;
                        } else if (response.message) {
                            // Показваме съобщение, ако има такова
                            this.showNotification(response.message, 'success');
                        } else if (response.error) {
                            // Показваме грешка, ако има такава
                            this.showNotification(response.error, 'error');
                            
                            // Показваме грешката в контейнера под формата
                            const responseContainer = document.getElementById('forgotten-response');
                            if (responseContainer) {
                                responseContainer.innerHTML = `
                                    <div class="bg-red-50 border-l-4 border-red-500 p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="ri-error-warning-line text-red-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-red-700">
                                                    ${response.error || 'Възникна грешка при обработката на заявката.'}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                responseContainer.classList.remove('hidden');
                            }
                        } else {
                            this.showNotification('Възникна грешка при обработката на заявката.', 'error');
                            
                            // Показваме обща грешка в контейнера
                            const responseContainer = document.getElementById('forgotten-response');
                            if (responseContainer) {
                                responseContainer.innerHTML = `
                                    <div class="bg-red-50 border-l-4 border-red-500 p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <i class="ri-error-warning-line text-red-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm text-red-700">
                                                    Възникна грешка при обработката на заявката. Моля, опитайте отново по-късно.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                responseContainer.classList.remove('hidden');
                            }
                        }
                    },
                    (error) => {
                        // Показване на нотификация
                        this.showNotification(error.message || 'Възникна грешка при изпращане на заявката.', 'error');
                        
                        // Връщане на бутона в нормално състояние
                        submitButton.innerHTML = 'Изпрати';
                        submitButton.disabled = false;
                        
                        // Показване на грешката в контейнера под формата
                        const responseContainer = document.getElementById('forgotten-response');
                        if (responseContainer) {
                            responseContainer.innerHTML = `
                                <div class="bg-red-50 border-l-4 border-red-500 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="ri-error-warning-line text-red-500 text-xl"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm text-red-700">
                                                ${error.message || 'Възникна грешка при изпращане на заявката. Моля, опитайте отново по-късно.'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            `;
                            responseContainer.classList.remove('hidden');
                        }
                    }
                );
            });
        },

        // ==================== WISHLIST ФУНКЦИОНАЛНОСТ ====================

        /**
         * Инициализира wishlist бутоните в продуктовите карти
         */
        initWishlistButtons: function() {
            const wishlistButtons = document.querySelectorAll('.wishlist-btn, .wishlist-button, [data-wishlist-btn]');

            wishlistButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const productId = this.getProductIdFromButton(button);
                    if (productId) {
                        this.toggleWishlist(productId, button);
                    }
                });
            });

            // Инициализиране на състоянието на бутоните при зареждане на страницата
            this.initWishlistButtonStates();
        },

        /**
         * Получава product_id от бутона или неговия родителски елемент
         */
        getProductIdFromButton: function(button) {
            // Проверяваме различни начини за получаване на product_id
            let productId = button.getAttribute('data-product-id');

            if (!productId) {
                // Търсим в родителския елемент (продуктовата карта)
                const productCard = button.closest('[data-product-id]');
                if (productCard) {
                    productId = productCard.getAttribute('data-product-id');
                }
            }

            if (!productId) {
                // Търсим в href атрибута
                const productLink = button.closest('a[href*="product_id="]');
                if (productLink) {
                    const match = productLink.href.match(/product_id=(\d+)/);
                    if (match) {
                        productId = match[1];
                    }
                }
            }

            return productId ? parseInt(productId) : null;
        },

        /**
         * Превключва състоянието на продукт в wishlist
         */
        toggleWishlist: function(productId, buttonElement) {
            // Проверяваме текущото състояние на бутона
            const isInWishlist = this.isButtonInWishlistState(buttonElement);

            if (isInWishlist) {
                this.removeFromWishlist(productId, buttonElement);
            } else {
                this.addToWishlist(productId, buttonElement);
            }
        },

        /**
         * Добавя продукт в wishlist
         */
        addToWishlist: function(productId, buttonElement) {
            // Показваме loading състояние
            this.setButtonLoadingState(buttonElement, true);

            const formData = new FormData();
            formData.append('product_id', productId);

            this.makeAjaxRequest('index.php?route=account/wishlist/add', formData,
                (response) => {
                    // Спираме loading състоянието
                    this.setButtonLoadingState(buttonElement, false);

                    if (response.success) {
                        // Успешно добавяне - задаваме wishlist състояние
                        this.setButtonWishlistState(buttonElement, true);
                        this.showNotification(response.message, 'success');

                        // Актуализираме брояча на wishlist
                        if (response.wishlist_count !== undefined) {
                            this.updateWishlistCounter(response.wishlist_count);
                        }

                        // Ако потребителят не е логнат, показваме съобщение за логин
                        if (response.login_required) {
                            setTimeout(() => {
                                this.showNotification(response.login_message, 'info');
                            }, 2000);
                        }

                    } else if (response.already_exists) {
                        // Продуктът вече е в wishlist - задаваме wishlist състояние
                        this.setButtonWishlistState(buttonElement, true);
                        this.showNotification(response.message, 'info');

                    } else {
                        // Грешка - възстановяваме оригиналното състояние
                        this.setButtonWishlistState(buttonElement, false);
                        this.showNotification(response.error || 'Възникна грешка при добавяне в списъка с любими.', 'error');
                    }
                },
                (error) => {
                    // Спираме loading и възстановяваме оригиналното състояние
                    this.setButtonLoadingState(buttonElement, false);
                    this.setButtonWishlistState(buttonElement, false);
                    this.showNotification('Възникна грешка при добавяне в списъка с любими.', 'error');
                }
            );
        },

        /**
         * Премахва продукт от wishlist
         */
        removeFromWishlist: function(productId, buttonElement) {
            // Проверяваме дали сме в account страницата с wishlist продукти
            const isAccountWishlistPage = buttonElement.closest('.wishlist-product-card') !== null;

            if (isAccountWishlistPage) {
                // Използваме специализирания метод за account страницата
                this.removeFromWishlistFromAccount(productId, buttonElement);
                return;
            }

            // Стандартна логика за други страници
            // Показваме loading състояние
            this.setButtonLoadingState(buttonElement, true);

            const formData = new FormData();
            formData.append('product_id', productId);

            this.makeAjaxRequest('index.php?route=account/wishlist/remove', formData,
                (response) => {
                    // Спираме loading състоянието
                    this.setButtonLoadingState(buttonElement, false);

                    if (response.success) {
                        // Успешно премахване - задаваме нормално състояние
                        this.setButtonWishlistState(buttonElement, false);
                        this.showNotification(response.message, 'success');

                        // Актуализираме брояча на wishlist
                        if (response.wishlist_count !== undefined) {
                            this.updateWishlistCounter(response.wishlist_count);
                        }

                    } else {
                        // Грешка - възстановяваме wishlist състояние
                        this.setButtonWishlistState(buttonElement, true);
                        this.showNotification(response.error || 'Възникна грешка при премахване от списъка с любими.', 'error');
                    }
                },
                (error) => {
                    // Спираме loading и възстановяваме wishlist състояние
                    this.setButtonLoadingState(buttonElement, false);
                    this.setButtonWishlistState(buttonElement, true);
                    this.showNotification('Възникна грешка при премахване от списъка с любими.', 'error');
                }
            );
        },

        /**
         * Проверява дали бутонът е в "в wishlist" състояние
         */
        isButtonInWishlistState: function(button) {
            const icon = button.querySelector('i');
            if (icon) {
                return icon.classList.contains('ri-heart-fill') ||
                       icon.classList.contains('ri-heart-3-fill') ||
                       button.classList.contains('in-wishlist') ||
                       button.classList.contains('text-primary');
            }
            return button.classList.contains('in-wishlist');
        },

        /**
         * Задава състоянието на бутона (в wishlist или не)
         */
        setButtonWishlistState: function(button, inWishlist) {
            const icon = button.querySelector('i');

            if (inWishlist) {
                // Продуктът е в wishlist - показваме попълнено сърце
                if (icon) {
                    icon.classList.remove('ri-heart-line', 'ri-heart-3-line');
                    icon.classList.add('ri-heart-fill');
                }
                button.classList.add('in-wishlist', 'text-primary');
                button.setAttribute('title', 'Премахни от любими');

            } else {
                // Продуктът не е в wishlist - показваме празно сърце
                if (icon) {
                    icon.classList.remove('ri-heart-fill', 'ri-heart-3-fill');
                    icon.classList.add('ri-heart-line');
                }
                button.classList.remove('in-wishlist', 'text-primary');
                button.setAttribute('title', 'Добави в любими');
            }
        },

        /**
         * Задава loading състояние на бутона
         */
        setButtonLoadingState: function(button, loading) {
            const icon = button.querySelector('i');

            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
                if (icon) {
                    // Запазваме текущата икона преди да я заменим с loader
                    button.setAttribute('data-original-icon', icon.className);
                    icon.className = 'ri-loader-4-line animate-spin';
                }
            } else {
                button.classList.remove('loading');
                button.disabled = false;
                if (icon) {
                    // Възстановяваме оригиналната икона или задаваме по подразбиране
                    const originalIcon = button.getAttribute('data-original-icon');
                    if (originalIcon) {
                        icon.className = originalIcon;
                        button.removeAttribute('data-original-icon');
                    } else {
                        // Ако няма запазена икона, задаваме по подразбиране
                        icon.className = 'ri-heart-line ri-lg';
                    }
                }
            }
        },

        /**
         * Инициализира състоянието на wishlist бутоните при зареждане на страницата
         */
        initWishlistButtonStates: function() {
            const wishlistButtons = document.querySelectorAll('.wishlist-btn, .wishlist-button, [data-wishlist-btn]');

            // Събираме всички product_id-та за batch проверка
            const productIds = [];
            const buttonMap = new Map();

            wishlistButtons.forEach(button => {
                const productId = this.getProductIdFromButton(button);
                if (productId) {
                    productIds.push(productId);
                    if (!buttonMap.has(productId)) {
                        buttonMap.set(productId, []);
                    }
                    buttonMap.get(productId).push(button);
                }
            });

            // Ако няма бутони, не правим нищо
            if (productIds.length === 0) return;

            // Проверяваме състоянието на всички продукти наведнъж
            this.checkMultipleWishlistStatus(productIds, buttonMap);
        },

        /**
         * Проверява състоянието на множество продукти в wishlist с batch заявка
         */
        checkMultipleWishlistStatus: function(productIds, buttonMap) {
            if (productIds.length === 0) return;

            const formData = new FormData();
            // Подаваме product_ids като масив, не като JSON string
            productIds.forEach(id => {
                formData.append('product_ids[]', id);
            });

            this.makeAjaxRequest('index.php?route=account/wishlist/checkMultiple', formData,
                (response) => {
                    if (response.success && response.wishlist_status) {
                        // Актуализираме състоянието на всички бутони
                        Object.keys(response.wishlist_status).forEach(productId => {
                            const inWishlist = response.wishlist_status[productId];
                            const buttons = buttonMap.get(parseInt(productId)) || [];
                            buttons.forEach(button => {
                                this.setButtonWishlistState(button, inWishlist);
                            });
                        });
                    }
                },
                (error) => {
                    // При грешка използваме fallback към индивидуални заявки
                    console.warn('Batch wishlist check failed, falling back to individual checks:', error);
                    productIds.forEach(productId => {
                        this.checkWishlistStatus(productId, (inWishlist) => {
                            const buttons = buttonMap.get(productId) || [];
                            buttons.forEach(button => {
                                this.setButtonWishlistState(button, inWishlist);
                            });
                        });
                    });
                }
            );
        },

        /**
         * Проверява дали продукт е в wishlist
         */
        checkWishlistStatus: function(productId, callback) {
            const formData = new FormData();
            formData.append('product_id', productId);

            this.makeAjaxRequest('index.php?route=account/wishlist/check', formData,
                (response) => {
                    if (response.success && callback) {
                        callback(response.in_wishlist);
                    }
                },
                (error) => {
                    // При грешка приемаме че не е в wishlist
                    if (callback) callback(false);
                }
            );
        },

        /**
         * Актуализира брояча на wishlist в header-а
         */
        updateWishlistCounter: function(count) {
            const counters = document.querySelectorAll('.wishlist-count, [data-wishlist-count]');
            counters.forEach(counter => {
                counter.textContent = count;

                // Показваме/скриваме брояча според стойността
                if (count > 0) {
                    counter.style.display = '';
                    counter.classList.remove('hidden');
                } else {
                    counter.style.display = 'none';
                    counter.classList.add('hidden');
                }
            });
        },

        /**
         * Получава текущия брой продукти в wishlist
         */
        getWishlistCount: function(callback) {
            this.makeAjaxRequest('index.php?route=account/wishlist/getCount', new FormData(),
                (response) => {
                    if (response.success && callback) {
                        callback(response.count);
                    }
                },
                (error) => {
                    if (callback) callback(0);
                }
            );
        },
        
        /**
         * Създава HTML елемент (карта) на продукт от wishlist
         * @param {Object} product Данни за продукта
         * @return {HTMLElement} DOM елемент на карта с продукта
         */
        createWishlistProductCard: function(product) {
            // Създаване на основния елемент на картата
            const card = document.createElement('a');
            card.href = product.href;
            card.className = 'block group wishlist-product-card';
            card.dataset.productId = product.product_id;
            card.dataset.productOptions = product.options ? JSON.stringify(product.options) : '{}';
            card.dataset.productInfo = product.product_info ? JSON.stringify(product.product_info) : '{}';
            
            // Създаване на HTML структурата на картата
            const priceHtml = product.special ? 
                `<span class="text-primary font-bold text-xl">${product.special_formatted}</span>
                 <span class="text-gray-400 line-through text-sm">${product.price_formatted}</span>` :
                `<span class="text-primary font-bold text-xl">${product.price_formatted}</span>`;
            
            // Попълване на HTML съдържанието на картата
            card.innerHTML = `
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform transform hover:scale-105">
                    <div class="relative">
                        <div class="h-64 overflow-hidden">
                            <img src="${product.image}" alt="${product.name}" class="w-full h-full object-cover object-top transition-transform duration-300 group-hover:scale-110">
                        </div>
                        <button class="absolute top-2 right-2 w-8 h-8 flex items-center justify-center bg-white rounded-full shadow cursor-pointer hover:text-primary transition-colors wishlist-btn in-wishlist text-primary"
                                data-product-id="${product.product_id}"
                                data-wishlist-btn="true"
                                title="Премахни от любими">
                            <i class="ri-heart-fill ri-lg"></i>
                        </button>
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">${product.name}</h3>
                        <div class="flex justify-between items-center">
                            <div class="flex flex-col">
                                ${priceHtml}
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap buyButton">Купи</button>
                        </div>
                    </div>
                </div>
            `;
            
            return card;
        }
});

// Initialize AccountModule
document.addEventListener('DOMContentLoaded', function() {
    AccountModule.init();
});

// Expose to global scope
window.AccountModule = AccountModule;

})();
