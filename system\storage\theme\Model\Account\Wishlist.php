<?php

namespace Theme25\Model\Account;

/**
 * Модел за управление на wishlist с пълна информация за продуктите
 * 
 * Този модел разширява стандартната OpenCart функционалност за wishlist
 * като добавя JOIN заявки за получаване на пълна информация за продуктите.
 *
 * @package Theme25\Model\Account
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Wishlist extends \Theme25\Model {


    /**
     * Добавя продукт в wishlist
     */
    public function addWishlist($product_id) {
        $product_id = (int)$product_id;
        $customer_id = (int)$this->customer->getId();
        
        if ($product_id <= 0 || $customer_id <= 0) {
            return false;
        }
        
        // Първо премахваме ако вече съществува (за да избегнем дублиране)
        $this->db->query("DELETE FROM " . DB_PREFIX . "customer_wishlist WHERE customer_id = '" . $customer_id . "' AND product_id = '" . $product_id . "'");
        
        // Добавяме новия запис
        $this->db->query("INSERT INTO " . DB_PREFIX . "customer_wishlist SET customer_id = '" . $customer_id . "', product_id = '" . $product_id . "', date_added = NOW()");
        
        return true;
    }

    /**
     * Премахва продукт от wishlist
     */
    public function deleteWishlist($product_id) {
        $product_id = (int)$product_id;
        $customer_id = (int)$this->customer->getId();
        
        if ($product_id <= 0 || $customer_id <= 0) {
            return false;
        }
        
        $this->db->query("DELETE FROM " . DB_PREFIX . "customer_wishlist WHERE customer_id = '" . $customer_id . "' AND product_id = '" . $product_id . "'");
        
        return true;
    }

    /**
     * Получава wishlist с пълна информация за продуктите
     * 
     * @param int $start Начална позиция за лимитиране на резултатите
     * @param int $limit Максимален брой резултати
     * @param string $sort Критерий за сортиране: newest, price-low, price-high, name-asc, name-desc
     * @return array Масив с продуктите в списъка с любими
     */
    public function getWishlist($start = 0, $limit = 20, $sort = 'newest') {
        $customer_id = (int)$this->customer->getId();
        
        if ($customer_id <= 0) {
            return [];
        }
        
        $start = (int)$start;
        $limit = (int)$limit;
        
        $sql = "SELECT 
                    w.product_id,
                    w.date_added as wishlist_date_added,
                    pd.name,
                    p.model,
                    p.image,
                    p.price,
                    p.status,
                    p.quantity,
                    pd.name as product_name,
                    pd.description,
                    pd.meta_title,
                    pd.meta_description,
                    pd.meta_keyword,
                    pd.tag,
                    ps.price as special_price
                FROM " . DB_PREFIX . "customer_wishlist w
                LEFT JOIN " . DB_PREFIX . "product p ON (w.product_id = p.product_id)
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN " . DB_PREFIX . "product_special ps ON (p.product_id = ps.product_id AND ps.customer_group_id = '" . (int)$this->config->get('config_customer_group_id') . "' AND ((ps.date_start = '0000-00-00' OR ps.date_start < NOW()) AND (ps.date_end = '0000-00-00' OR ps.date_end > NOW())))
                WHERE w.customer_id = '" . $customer_id . "'
                AND p.status = '1'";
        
        // Определяне на сортирането според подадения параметър
        switch ($sort) {
            case 'price-low':
                $sql .= " ORDER BY COALESCE(ps.price, p.price) ASC, w.date_added DESC";
                break;
            case 'price-high':
                $sql .= " ORDER BY COALESCE(ps.price, p.price) DESC, w.date_added DESC";
                break;
            case 'name-asc':
                $sql .= " ORDER BY pd.name ASC, w.date_added DESC";
                break;
            case 'name-desc':
                $sql .= " ORDER BY pd.name DESC, w.date_added DESC";
                break;
            case 'newest':
            default:
                $sql .= " ORDER BY w.date_added DESC";
                break;
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT " . $start . "," . $limit;
        }

        $query = $this->db->query($sql);
 
        $results = [];
        foreach ($query->rows as $result) {
            $results[] = [
                'product_id' => $result['product_id'],
                'name' => $result['product_name'] ?: $result['name'],
                'model' => $result['model'],
                'image' => $result['image'],
                'price' => $result['price'],
                'special' => $result['special_price'],
                'status' => $result['status'],
                'quantity' => $result['quantity'],
                'description' => $result['description'],
                'date_added' => $result['wishlist_date_added']
            ];
        }
        
        return $results;
    }

    /**
     * Получава общия брой продукти в wishlist
     */
    public function getTotalWishlist() {
        $customer_id = (int)$this->customer->getId();
        
        if ($customer_id <= 0) {
            return 0;
        }
        
        $query = $this->db->query("SELECT COUNT(*) AS total 
                                  FROM " . DB_PREFIX . "customer_wishlist w
                                  LEFT JOIN " . DB_PREFIX . "product p ON (w.product_id = p.product_id)
                                  WHERE w.customer_id = '" . $customer_id . "' 
                                  AND p.status = '1'");
        
        return (int)$query->row['total'];
    }

    /**
     * Проверява дали продукт е в wishlist
     */
    public function inWishlist($product_id) {
        $product_id = (int)$product_id;
        $customer_id = (int)$this->customer->getId();
        
        if ($product_id <= 0 || $customer_id <= 0) {
            return false;
        }
        
        $query = $this->db->query("SELECT COUNT(*) AS total 
                                  FROM " . DB_PREFIX . "customer_wishlist 
                                  WHERE customer_id = '" . $customer_id . "' 
                                  AND product_id = '" . $product_id . "'");
        
        return (int)$query->row['total'] > 0;
    }

    /**
     * Получава множество продукти от wishlist по техните ID-та
     */
    public function getWishlistByProductIds($product_ids) {
        $customer_id = (int)$this->customer->getId();
        
        if ($customer_id <= 0 || empty($product_ids) || !is_array($product_ids)) {
            return [];
        }
        
        // Почистваме и валидираме product_ids
        $clean_ids = [];
        foreach ($product_ids as $id) {
            $id = (int)$id;
            if ($id > 0) {
                $clean_ids[] = $id;
            }
        }
        
        if (empty($clean_ids)) {
            return [];
        }
        
        $ids_string = implode(',', $clean_ids);
        
        $query = $this->db->query("SELECT product_id 
                                  FROM " . DB_PREFIX . "customer_wishlist 
                                  WHERE customer_id = '" . $customer_id . "' 
                                  AND product_id IN (" . $ids_string . ")");
        
        $wishlist_ids = [];
        foreach ($query->rows as $row) {
            $wishlist_ids[] = (int)$row['product_id'];
        }
        
        return $wishlist_ids;
    }
}
