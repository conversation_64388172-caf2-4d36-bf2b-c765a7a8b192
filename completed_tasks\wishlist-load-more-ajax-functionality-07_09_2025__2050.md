# Имплементация на AJAX функционалност "Зареди още продукти" в Wishlist

## Описание на задачата
Имплементиране на AJAX функционалност "Зареди още продукти" в контролерите Wishlist и Account, за постепенно зареждане на продукти в списъка с любими (wishlist).

## Изпълнени задачи

### 1. Добавен нов AJAX метод `loadMore()` в Wishlist контролера
Имплементиран нов AJAX метод в контролера `Wishlist.php`, който:
- Приема параметри за страница, лимит и сортиране
- Връща JSON отговор с продукти, общ брой и информация дали има още за зареждане
- Използва съществуващия модел за работа с wishlist продукти

```php
/**
 * AJAX метод за зареждане на още продукти от wishlist
 */
public function loadMore() {
    $json = [];

    try {
        // Проверка за AJAX заявка
        if (!$this->isAjaxRequest()) {
            throw new \Exception('Невалидна заявка');
        }

        // Получаване на параметрите за пагинация
        $page = max(1, (int)$this->requestPost('page', 1));
        $limit = max(1, (int)$this->requestPost('limit', 8));
        $offset = ($page - 1) * $limit;
        $sort = $this->requestPost('sort', 'newest');

        // Получаване на продуктите с offset и limit
        if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
            $results = $this->themeWishlistModel->getWishlist($offset, $limit, $sort);
            
            // Обработка на продуктите и връщане на JSON отговор
            // ...
        }
    } catch (\Exception $e) {
        $json['success'] = false;
        $json['error'] = $e->getMessage();
    }

    $this->setJSONResponseOutput($json);
}
```

### 2. Актуализиран Model\Account\Wishlist.php за поддръжка на сортиране
Обновен методът `getWishlist()` в модела за поддръжка на различни типове сортиране:
- По дата (най-нови)
- По цена (от ниска към висока)
- По цена (от висока към ниска)
- По име (възходящо и низходящо)

```php
/**
 * Получава wishlist с пълна информация за продуктите
 * 
 * @param int $start Начална позиция за лимитиране на резултатите
 * @param int $limit Максимален брой резултати
 * @param string $sort Критерий за сортиране: newest, price-low, price-high, name-asc, name-desc
 * @return array Масив с продуктите в списъка с любими
 */
public function getWishlist($start = 0, $limit = 20, $sort = 'newest') {
    // ...

    // Определяне на сортирането според подадения параметър
    switch ($sort) {
        case 'price-low':
            $sql .= " ORDER BY COALESCE(ps.price, p.price) ASC, w.date_added DESC";
            break;
        case 'price-high':
            $sql .= " ORDER BY COALESCE(ps.price, p.price) DESC, w.date_added DESC";
            break;
        // ... и други типове сортиране
    }
    
    // ...
}
```

### 3. Актуализирани шаблони за поддръжка на AJAX функционалността
Добавени data атрибути към бутона "Зареди още продукти" в шаблоните:
- `wishlist.twig`
- `account.twig` (в секцията с любими продукти)

```html
<button class="bg-white text-primary px-6 py-3 rounded-button font-medium border border-primary hover:bg-primary/5 transition-colors whitespace-nowrap" id="loadMoreWishlist"
    data-page="2"
    data-limit="8"
    data-loaded="{{ wishlist|length }}"
    data-total="{{ wishlist_total }}">
    Зареди още продукти
</button>
```

### 4. Актуализиран JavaScript за работа с AJAX заявките
Обновена функцията `loadMoreWishlistProducts()` в `account.js` за изпращане на AJAX заявки и динамично добавяне на продукти:

```javascript
loadMoreWishlistProducts: function() {
    // Реализация за зареждане на още продукти от wishlist чрез AJAX
    const loadMoreBtn = document.getElementById('loadMoreWishlist');
    if (!loadMoreBtn) return;
    
    // Показваме индикатор за зареждане
    loadMoreBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i> Зареждане...';
    loadMoreBtn.disabled = true;
    
    // Получаваме данните от бутона
    const page = parseInt(loadMoreBtn.dataset.page || '2');
    const limit = parseInt(loadMoreBtn.dataset.limit || '8');
    const loaded = parseInt(loadMoreBtn.dataset.loaded || '0');
    const total = parseInt(loadMoreBtn.dataset.total || '0');
    const sort = document.getElementById('wishlistSortSelect')?.value || 'newest';
    
    // AJAX заявка към loadMore метода
    const formData = new FormData();
    formData.append('page', page);
    formData.append('limit', limit);
    formData.append('sort', sort);
    
    this.makeAjaxRequest('index.php?route=account/wishlist/loadMore', formData,
        (response) => {
            // Обработка на отговора и добавяне на нови продукти
            // ...
        },
        (error) => {
            // Обработка на грешки
            // ...
        }
    );
}
```

### 5. Добавена функция за създаване на продуктови карти
Добавена нова функция `createWishlistProductCard()` в `account.js` за динамично създаване на HTML елементи за новите продукти:

```javascript
/**
 * Създава HTML елемент (карта) на продукт от wishlist
 * @param {Object} product Данни за продукта
 * @return {HTMLElement} DOM елемент на карта с продукта
 */
createWishlistProductCard: function(product) {
    // Създаване на HTML структурата на картата
    // ...
}
```

## Резултат
Сега функционалността "Зареди още продукти" работи чрез AJAX, като зарежда постепенно по още 8 продукта, докато не се покажат всички продукти от списъка с любими. Тази функционалност е споделена между контролерите Account и Wishlist, като и двата използват един и същ AJAX метод за зареждане на данните.
