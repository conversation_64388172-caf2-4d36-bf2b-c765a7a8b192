<?php

namespace Theme25\Frontend\Controller\Account;

class Reset extends \Theme25\FrontendController {

    private $error = [];
    
    public function __construct($registry) {
        parent::__construct($registry, 'account/reset');
    }
    
    public function index() {

        $code = $this->requestGet('code');

        // Проверка дали потребителят вече е логнат
        if ($this->customer->isLogged()) {
            $this->response->redirect($this->getLink('account/account', '', true));
        }

        // Проверка за код в URL-а
        if (empty($code)) {
            // Ако няма код, пренасочваме към страницата за вход
            $this->response->redirect($this->getLink('account/login', '', true));
            return;
        }

        // Зареждаме модела за клиенти
        $this->loadModelAs('account/customer', 'customerModel');
        
        // Търсим клиент по кода за възстановяване
        $customerInfo = $this->customerModel->getCustomerByCode($code);

        
        if (!$customerInfo) {
            // Ако няма такъв клиент, показваме грешка и пренасочваме към вход
            $this->error['code'] = 'Невалиден или изтекъл код за възстановяване на парола.';
        }
        
        $this->setTitle('Възстановяване на парола');
        $this->addFrontendScriptWithVersion('account.js', 'footer');

        // Обработка на формата при изпращане
        if ($this->isPostRequest() && $this->validateForm()) {

            // Ако данните са валидни, променяме паролата
            $this->customerModel->editPassword($customerInfo['email'], $this->requestPost('password'));

            // Изтриваме кода за възстановяване
            $this->removeCode($customerInfo['email']);

            // Показваме съобщение за успешна промяна
            $this->session->data['success'] = 'Паролата ви беше променена успешно.';

            // Пренасочваме към страницата за вход
            $this->response->redirect($this->getLink('account/login', '', true));
            return;
        }

        // Подготовка на данните за изгледа
        $data = [];
        
        // URL-и за формата
        $routes = [
            'action' => 'account/reset',
            'login' => 'account/login'
        ];

        $link_args = [
            'action' => 'code=' . $code,
        ];
        
        $data['links'] = $this->getLinks($routes, $link_args);
        $data['code'] = $code;
        
        // Добавяне на грешки
        $data['error_password'] = isset($this->error['password']) ? $this->error['password'] : '';
        $data['error_confirm'] = isset($this->error['confirm']) ? $this->error['confirm'] : '';
        $data['error_code'] = isset($this->error['code']) ? $this->error['code'] : '';
        
        // Данни от формата
        $data['password'] = $this->requestPost('password') ? $this->requestPost('password') : '';
        $data['confirm'] = $this->requestPost('confirm') ? $this->requestPost('confirm') : '';
        
        $this->renderTemplateWithDataAndOutput('account/reset', $data);
    }
    
    /**
     * Валидира формата за възстановяване на парола
     */
    protected function validateForm() {
        // Проверка за дължина на паролата (между 4 и 40 символа)
        $password = html_entity_decode($this->requestPost('password'), ENT_QUOTES, 'UTF-8');
        if (mb_strlen($password) < 4 || mb_strlen($password) > 40) {
            $this->error['password'] = 'Паролата трябва да е между 4 и 40 символа.';
        }
        
        // Проверка дали потвърждението съвпада с паролата
        if ($this->requestPost('confirm') != $this->requestPost('password')) {
            $this->error['confirm'] = 'Паролата за потвърждение не съвпада с въведената парола.';
        }
        
        // Връщаме true ако няма грешки
        return empty($this->error);
    }

    private function removeCode($email) {
        if(CM()->shouldUseSecondDb('account/reset')) {
            $db = CM()->getSecondDatabase();
        } else {
            $db = $this->db;
        }

        $sql = "UPDATE `" . DB_PREFIX . "customer` SET code = '' WHERE LCASE(email) = '" . $this->db->escape(utf8_strtolower($email)) . "'";

        try {
            $db->query($sql);
        } catch (Exception $e) {
            F()->log->developer($e->getMessage(), __FILE__, __LINE__);
        }   
    }
}
