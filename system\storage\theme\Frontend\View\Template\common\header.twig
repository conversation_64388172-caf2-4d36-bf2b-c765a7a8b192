<!-- Header -->
<header class="sticky top-0 z-50 bg-white shadow-md">
	<div class="container mx-auto px-4 py-3">
		<div
			class="flex items-center justify-between">
			<!-- Logo -->
			<div class="w-40">
				<a href="{{ home_url }}"><img src="{{ logo_url }}" alt="{{ site_name }}" class="h-12"></a>
			</div>
			<!-- Search and Ask Betty -->
			<div class="flex-1 mx-8">
				<div class="relative flex items-center gap-3">
					<div class="relative flex-1" id="productSearchContainer">
						<input
							type="text"
							id="productSearchInput"
							placeholder="{{ search_placeholder }}"
							class="w-full py-3 px-5 bg-gray-50 border border-[rgb(118,118,118)] rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:bg-white transition-all shadow-sm"
							autocomplete="off"
							spellcheck="false"
						>
						<button
							id="productSearchButton"
							type="button"
							class="absolute right-3 top-1/2 -translate-y-1/2 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-primary transition-colors"
							title="Търсене"
						>
							<i class="ri-search-line ri-lg"></i>
						</button>
					</div>
					<button id="askBettyBtn" class="flex items-center gap-2 bg-primary text-white px-5 py-3 rounded-full whitespace-nowrap hover:bg-opacity-90 transition-colors shadow-lg shadow-primary/20">
						<i class="ri-message-3-line"></i>
						<span>{{ ask_betty_text }}</span>
					</button>
				</div>
			</div>
			<!-- User Controls -->
			<div class="flex items-center space-x-8">
				<div class="flex flex-col items-center cursor-pointer group">
					<a href="{{ wishlist_url }}" class="flex flex-col items-center">
						<div class="w-6 h-6 flex items-center justify-center text-gray-700 group-hover:text-primary">
							<i class="ri-heart-line ri-lg"></i>
						</div>
						<span class="text-xs mt-1 text-gray-600 group-hover:text-primary">Любими</span>
					</a>
				</div>
				<div class="flex flex-col items-center cursor-pointer group">
					<a href="{{ account_url }}" class="flex flex-col items-center">
						<div class="w-6 h-6 flex items-center justify-center text-gray-700 group-hover:text-primary">
							<i class="ri-user-line ri-lg"></i>
						</div>
						<span class="text-xs mt-1 text-gray-600 group-hover:text-primary">Профил</span>
					</a>
				</div>
				<div class="flex flex-col items-center cursor-pointer group relative" id="cartToggle">
					<div class="w-6 h-6 flex items-center justify-center text-gray-700 group-hover:text-primary">
						<i class="ri-shopping-cart-line ri-lg"></i>
						<span id="cartCountBadge" class="absolute -top-1 -right-1 bg-primary text-white text-[10px] rounded-full w-4 h-4 flex items-center justify-center{{ cart_count > 0 ? '' : ' hidden' }}">{{ cart_count }}</span>
					</div>
					<span class="text-xs mt-1 text-gray-600 group-hover:text-primary">Количка</span>
				</div>
				{% include 'checkout/slide_cart.twig' %}
			</div>
		</div>
		<!-- Main Navigation with Mega Menu -->
		{% if menu_data is not empty %}
			{% include 'common/mega_menu.twig' with {'menu_items': menu_data} %}
		{% endif %}
</header>

{# Modal Templates - включени като template елементи за по-бърз достъп #}
<template id="productOptionsModalTemplate">
	{% include 'catalog/product/add_to_cart_modal.twig' %}
</template>

<template id="cartConfirmationModalTemplate">
	{% include 'checkout/cart_confirmation_modal.twig' %}
</template>
