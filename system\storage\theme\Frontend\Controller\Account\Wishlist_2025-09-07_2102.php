<?php

namespace Theme25\Frontend\Controller\Account;

class Wishlist extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'account/wishlist');

         // Проверка дали потребителят е логнат
         if (!$this->customer->isLogged()) {
            $this->response->redirect($this->getLink('account/login', '', true));
        }

        // Зареждаме специализирания wishlist модел от темата
        $this->loadModelAs('account/wishlist', 'themeWishlistModel');
        
    }

    public function index() {

        $this->loadModelAs('tool/image', 'imageModel');

         // Зареждане на JavaScript и CSS файлове
         $this->addFrontendScriptWithVersion('account.js', 'footer');

         $data['wishlist'] = $this->getWishlistProducts(8); // Лимит до 8 продукта
         $data['wishlist_total'] = $this->getWishlistTotal();
         $data['links'] = [
            'shop' => HTTPS_SERVER,
        ];

        // Рендериране на шаблона
        return $this->renderTemplateWithDataAndOutput('account/wishlist', $data);
        
    }

    /**
     * Помощен метод за получаване на любими продукти
     */
    private function getWishlistProducts($limit = 8) {
        $wishlist = [];

        if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
            $results = $this->themeWishlistModel->getWishlist(0, $limit);
            foreach ($results as $result) {
                // Зареждаме изображението
                $image = '';
                if ($result['image']) {
                    $this->loadModelAs('tool/image', 'imageModel');
                    $image = $this->imageModel->resize($result['image'], 600, 600);
                }

                $wishlist[] = [
                    'product_id' => $result['product_id'],
                    'name' => $result['name'],
                    'model' => $result['model'],
                    'image' => $image,
                    'price' => $result['price'], // Сурова цена
                    'special' => $result['special'], // Сурова специална цена
                    'price_formatted' => $this->formatCurrency($result['price']),
                    'special_formatted' => $result['special'] ? $this->formatCurrency($result['special']) : null,
                    'href' => $this->getLink('product/product', 'product_id=' . $result['product_id']),
                    'date_added' => $result['date_added']
                ];
            }
        }

        return $wishlist;
    }

    /**
     * Помощен метод за получаване на общ брой любими продукти
     */
    private function getWishlistTotal() {
        $total = 0;

        if (is_callable([$this->themeWishlistModel, 'getTotalWishlist'])) {
            $total = $this->themeWishlistModel->getTotalWishlist();
        }

        return $total;
    }

    /**
     * AJAX метод за добавяне на продукт в wishlist
     */
    public function add() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');
            
            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            // Зареждаме модела за продукти за валидация
            $this->loadModelAs('catalog/product', 'productModel');
            $product_info = $this->productModel->getProduct($product_id);

            if (!$product_info) {
                throw new \Exception('Продуктът не е намерен');
            }

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - добавяме в базата данни

                // Проверяваме дали продуктът вече е в wishlist
                if ($this->themeWishlistModel->inWishlist($product_id)) {
                    $json['success'] = false;
                    $json['message'] = 'Продуктът вече е в списъка с любими продукти';
                    $json['already_exists'] = true;
                } else {
                    // Добавяме продукта в wishlist
                    $this->themeWishlistModel->addWishlist($product_id);

                    $json['success'] = true;
                    $json['message'] = 'Продуктът е добавен в списъка с любими продукти';
                    $json['product_name'] = $product_info['name'];

                    // Получаваме актуализирания брой продукти в wishlist
                    $json['wishlist_count'] = $this->themeWishlistModel->getTotalWishlist();
                }

            } else {
                // Потребителят не е логнат - използваме сесията
                if (!isset($this->session->data['wishlist'])) {
                    $this->session->data['wishlist'] = [];
                }

                if (in_array($product_id, $this->session->data['wishlist'])) {
                    $json['success'] = false;
                    $json['message'] = 'Продуктът вече е в списъка с любими продукти';
                    $json['already_exists'] = true;
                } else {
                    $this->session->data['wishlist'][] = $product_id;
                    $this->session->data['wishlist'] = array_unique($this->session->data['wishlist']);
                    
                    $json['success'] = true;
                    $json['message'] = 'Продуктът е добавен в списъка с любими продукти';
                    $json['product_name'] = $product_info['name'];
                    $json['login_required'] = true;
                    $json['login_message'] = 'За да запазите списъка с любими продукти, моля влезте в профила си';
                    
                    // Брой продукти в сесийния wishlist
                    $json['wishlist_count'] = count($this->session->data['wishlist']);
                }
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за премахване на продукт от wishlist
     */
    public function remove() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');
            
            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - премахваме от базата данни
                $this->themeWishlistModel->deleteWishlist($product_id);

                $json['success'] = true;
                $json['message'] = 'Продуктът е премахнат от списъка с любими продукти';

                // Получаваме актуализирания брой продукти в wishlist
                $json['wishlist_count'] = $this->themeWishlistModel->getTotalWishlist();

            } else {
                // Потребителят не е логнат - премахваме от сесията
                if (isset($this->session->data['wishlist'])) {
                    $key = array_search($product_id, $this->session->data['wishlist']);
                    if ($key !== false) {
                        unset($this->session->data['wishlist'][$key]);
                        $this->session->data['wishlist'] = array_values($this->session->data['wishlist']);
                    }
                }
                
                $json['success'] = true;
                $json['message'] = 'Продуктът е премахнат от списъка с любими продукти';
                
                // Брой продукти в сесийния wishlist
                $json['wishlist_count'] = isset($this->session->data['wishlist']) ? count($this->session->data['wishlist']) : 0;
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за проверка дали продукт е в wishlist
     */
    public function check() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_id = (int)$this->requestPost('product_id');
            
            if ($product_id <= 0) {
                throw new \Exception('Невалиден продукт');
            }

            $in_wishlist = false;

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - проверяваме в базата данни
                $in_wishlist = $this->themeWishlistModel->inWishlist($product_id);

            } else {
                // Потребителят не е логнат - проверяваме в сесията
                if (isset($this->session->data['wishlist'])) {
                    $in_wishlist = in_array($product_id, $this->session->data['wishlist']);
                }
            }

            $json['success'] = true;
            $json['in_wishlist'] = $in_wishlist;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за получаване на броя продукти в wishlist
     */
    public function getCount() {
        $json = [];

        try {
            $count = 0;

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - броим от базата данни
                $count = $this->themeWishlistModel->getTotalWishlist();
            } else {
                // Потребителят не е логнат - броим от сесията
                $count = isset($this->session->data['wishlist']) ? count($this->session->data['wishlist']) : 0;
            }

            $json['success'] = true;
            $json['count'] = $count;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за проверка на множество продукти в wishlist наведнъж (batch операция)
     */
    public function checkMultiple() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $product_ids = $this->requestPost('product_ids');

            // Ако product_ids е подаден като JSON string, декодираме го
            if (is_string($product_ids)) {
                $product_ids = json_decode($product_ids, true);
            }

            if (!is_array($product_ids) || empty($product_ids)) {
                throw new \Exception('Невалидни product_ids');
            }

            // Валидираме и почистваме product_ids
            $valid_product_ids = [];
            foreach ($product_ids as $id) {
                $id = (int)$id;
                if ($id > 0) {
                    $valid_product_ids[] = $id;
                }
            }

            if (empty($valid_product_ids)) {
                throw new \Exception('Няма валидни product_ids');
            }

            $wishlist_status = [];

            // Проверяваме дали потребителят е логнат
            if ($this->customer->isLogged()) {
                // Потребителят е логнат - използваме batch проверка от новия модел
                $wishlist_product_ids = $this->themeWishlistModel->getWishlistByProductIds($valid_product_ids);

                // Проверяваме всеки продукт
                foreach ($valid_product_ids as $product_id) {
                    $wishlist_status[$product_id] = in_array($product_id, $wishlist_product_ids);
                }

            } else {
                // Потребителят не е логнат - проверяваме в сесията
                $session_wishlist = isset($this->session->data['wishlist']) ? $this->session->data['wishlist'] : [];

                foreach ($valid_product_ids as $product_id) {
                    $wishlist_status[$product_id] = in_array($product_id, $session_wishlist);
                }
            }

            $json['success'] = true;
            $json['wishlist_status'] = $wishlist_status;

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }
    
    /**
     * AJAX метод за зареждане на още продукти от wishlist
     */
    public function loadMore() {
        $json = [];

        try {
            // Проверка за AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            // Получаване на параметрите за пагинация
            $page = max(1, (int)$this->requestPost('page', 1));
            $limit = max(1, (int)$this->requestPost('limit', 8));
            $offset = ($page - 1) * $limit;
            $sort = $this->requestPost('sort', 'newest');

            // Получаване на продуктите с offset и limit
            if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
                $results = $this->themeWishlistModel->getWishlist($offset, $limit, $sort);
                
                $products = [];
                foreach ($results as $result) {
                    // Зареждаме изображението
                    $image = '';
                    if ($result['image']) {
                        $this->loadModelAs('tool/image', 'imageModel');
                        $image = $this->imageModel->resize($result['image'], 600, 600);
                    }

                    $products[] = [
                        'product_id' => $result['product_id'],
                        'name' => $result['name'],
                        'model' => $result['model'],
                        'image' => $image,
                        'price' => $result['price'], // Сурова цена
                        'special' => $result['special'], // Сурова специална цена
                        'price_formatted' => $this->formatCurrency($result['price']),
                        'special_formatted' => $result['special'] ? $this->formatCurrency($result['special']) : null,
                        'href' => $this->getLink('product/product', 'product_id=' . $result['product_id']),
                        'date_added' => $result['date_added']
                    ];
                }

                $total = $this->getWishlistTotal();
                $loaded = $offset + count($products);

                $json['success'] = true;
                $json['products'] = $products;
                $json['total'] = $total;
                $json['loaded'] = $loaded;
                $json['has_more'] = $loaded < $total;
            } else {
                throw new \Exception('Функцията за зареждане на wishlist продукти не е налична');
            }

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }
}
