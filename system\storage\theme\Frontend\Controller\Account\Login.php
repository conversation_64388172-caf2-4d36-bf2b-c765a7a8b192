<?php

namespace Theme25\Frontend\Controller\Account;

class Login extends \Theme25\FrontendController {

    public function __construct($registry) {
        parent::__construct($registry, 'account/login');
    }

	public function index() {
        // Ако потребителят вече е вписан, пренасочваме го
		if ($this->customer->isLogged()) {
            // Ако е AJAX заявка, връщаме JSON, в противен случай - стандартно пренасочване
            if ($this->isPostRequest()) {
                $this->jsonOutput([ 'success' => true, 'redirect' => $this->getLink('account/account', '', true)]);
                return;
            } else {
                $this->response->redirect($this->getLink('account/account', '', true));
            }
		}

        // Обработка на POST заявка от формата за вход (AJAX)
        if ($this->isPostRequest()) {
            $this->processLoginAttempt();
            return; // processLoginAttempt ще прекрати изпълнението с JSON отговор
        }

        // Стандартно зареждане на страницата при GET заявка
		$this->setTitle('Вход в профила');
        $this->addFrontendScriptWithVersion('account.js', 'footer');

		$routes = [
            'action'         => 'account/login',
            'register'       => 'account/register',
            'forgotten'      => 'account/forgotten',
            // 'facebook_login' => 'extension/module/social_login/facebook',
            'facebook_login' => 'account/login',
            // 'google_login'   => 'extension/module/social_login/google'
            'google_login'   => 'account/login'
        ];

        $data['links'] = $this->getLinks($routes);
		$this->renderTemplateWithDataAndOutput('account/login', $data);
	}

    private function processLoginAttempt()
    {
        $json = [];
        $this->loadModelAs('account/customer', 'customerModel');

        // Проверка дали потребителят е въвел правилни данни
        if (!$this->customer->login($this->requestPost('email'), html_entity_decode($this->requestPost('password'), ENT_QUOTES, 'UTF-8'))) {
            $json['error'] = 'Невалидни данни за вход';
        }

        $customer_info = $this->customerModel->getCustomerByEmail($this->requestPost('email'));
        if ($customer_info && !$customer_info['status']) {
            $json['error'] = 'Достъпът е ограничен';
        }

        if (empty($json['error'])) {
            // Успешен вход - премахваме токена за забравена парола, ако има такъв
            $this->customerModel->deleteLoginAttempts($this->requestPost('email'));

            // Генерираме URL за пренасочване
            // TODO: Логика за пренасочване към предишна страница, ако е запазена в сесията
            $json['redirect'] = $this->getLink('account/account');
            $json['success'] = true;
        }

        $this->jsonResponse($json);
    }
}


