<main class="py-12">
    <div class="container mx-auto px-4">
        <div class="mx-auto">
            <!-- Page Title -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-800">Любими продукти</h1>
            </div>
                <!-- Favorites Section -->
                <div class="profile-section" id="favorites">
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex space-x-4">
                            <div class="relative">
                                <select id="wishlistSortSelect" class="appearance-none px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 pr-8">
                                    <option value="">Сортиране по</option>
                                    <option value="newest">Най-нови</option>
                                    <option value="price-low">Цена (ниска към висока)</option>
                                    <option value="price-high">Цена (висока към ниска)</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <i class="ri-arrow-down-s-line text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if wishlist and wishlist|length > 0 %}
                    <div id="wishlistProductsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {% for product in wishlist %}
                        <a href="{{ ' ' }}{{ product.href }}" class="block group wishlist-product-card"
                           data-product-id="{{ ' ' }}{{ product.product_id }}"
                           data-product-options="{{ ' ' }}{{ product.options|default('{}')|json_encode|e('html_attr') }}"
                           data-product-info="{{ ' ' }}{{ product.product_info|default('{}')|json_encode|e('html_attr') }}">
                            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform transform hover:scale-105">
                                <div class="relative">
                                    <div class="h-64 overflow-hidden">
                                        <img src="{{ ' ' }}{{ product.image }}" alt="{{ ' ' }}{{ product.name }}" class="w-full h-full object-cover object-top transition-transform duration-300 group-hover:scale-110">
                                    </div>
                                    <button class="absolute top-2 right-2 w-8 h-8 flex items-center justify-center bg-white rounded-full shadow cursor-pointer hover:text-primary transition-colors wishlist-btn in-wishlist text-primary"
                                            data-product-id="{{ ' ' }}{{ product.product_id }}"
                                            data-wishlist-btn="true"
                                            title="Премахни от любими">
                                        <i class="ri-heart-fill ri-lg"></i>
                                    </button>
                                    {% if product.labels %}
                                        <div class="absolute top-2 left-2 space-y-1">
                                            {% for label in product.labels %}
                                                <span class="{{ ' ' }}{{ label.class }}">{{ ' ' }}{{ label.text }}</span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="p-4">
                                    <h3 class="text-lg font-semibold mb-4 min-h-[56px] leading-7">{{ ' ' }}{{ product.name }}</h3>
                                    <div class="flex justify-between items-center">
                                        <div class="flex flex-col">
                                            {% if product.special is not null and product.special is not same as(false) %}
                                                <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.special_formatted }}</span>
                                                <span class="text-gray-400 line-through text-sm">{{ ' ' }}{{ product.price_formatted }}</span>
                                            {% else %}
                                                <span class="text-primary font-bold text-xl">{{ ' ' }}{{ product.price_formatted }}</span>
                                            {% endif %}
                                        </div>
                                        <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-opacity-90 whitespace-nowrap buyButton">Купи</button>
                                    </div>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% if wishlist_total > wishlist|length %}
                    <div class="mt-8 flex justify-center">
                        <button class="bg-white text-primary px-6 py-3 rounded-button font-medium border border-primary hover:bg-primary/5 transition-colors whitespace-nowrap" id="loadMoreWishlist"
                            data-page="2"
                            data-limit="8"
                            data-loaded="{{ wishlist|length }}"
                            data-total="{{ wishlist_total }}">
                            Зареди още продукти
                        </button>
                    </div>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-12">
                        <i class="ri-heart-line text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Нямате любими продукти</h3>
                        <p class="text-gray-500 mb-6">Започнете да добавяте продукти в списъка с любими, за да ги видите тук.</p>
                        <a href="{{ links.shop|default('index.php') }}" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-opacity-90 transition-colors">
                            Разгледай продукти
                        </a>
                    </div>
                    {% endif %}
                </div>

                
            </div>
        </div>
    </div>
</main>
