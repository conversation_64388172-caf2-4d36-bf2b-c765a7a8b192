# Поправка на функционалността за зареждане на още продукти от wishlist

## Промпт (Подканата)
Анализирай и поправи функционалността за зареждане на още продукти от списъка "Любими продукти" (wishlist) в следните файлове:

**Контролери:**
- `system/storage/theme/Frontend/Controller/Account/Wishlist.php`
- `system/storage/theme/Frontend/Controller/Account/Account.php`

**Шаблони:**
- `system/storage/theme/Frontend/View/Template/account/wishlist.twig`
- `system/storage/theme/Frontend/View/Template/account/account.twig`

**JavaScript:**
- `system/storage/theme/Frontend/View/Javascript/account.js`

**Проблем:** Функционалността за зареждане на още продукти от wishlist не работи правилно.

**Цел:** Когато има повече от 8 продукта в списъка с любими продукти, да се визуализират следващите продукти на порции от по 8, докато всички се покажат.

## Анализ на проблемите

След анализа на кода идентифицирах следните проблеми:

1. **Липсва `loadMore` метод в `Account.php` контролера** - JavaScript кодът прави AJAX заявка към `account/wishlist/loadMore`, но в `Account.php` няма такъв метод
2. **Дублиране на код** - И двата контролера имат еднакви методи за работа с wishlist (`getWishlistProducts`, `getWishlistTotal`)
3. **Неправилни AJAX endpoints** - JavaScript кодът винаги извиква `account/wishlist/loadMore`, независимо от контекста

## Направени промени

### 1. Добавен `loadMore` метод в Account.php контролера

Добавих AJAX метод `loadMore` в `system/storage/theme/Frontend/Controller/Account/Account.php`, който използва същата логика като в `Wishlist.php` контролера:

```php
/**
 * AJAX метод за зареждане на още продукти от wishlist
 */
public function loadMore() {
    $json = [];

    try {
        // Проверка за AJAX заявка
        if (!$this->isAjaxRequest()) {
            throw new \Exception('Невалидна заявка');
        }

        // Получаване на параметрите за пагинация
        $page = max(1, (int)$this->requestPost('page', 1));
        $limit = max(1, (int)$this->requestPost('limit', 8));
        $offset = ($page - 1) * $limit;
        $sort = $this->requestPost('sort', 'newest');

        // Получаване на продуктите с offset и limit
        if (is_callable([$this->themeWishlistModel, 'getWishlist'])) {
            $results = $this->themeWishlistModel->getWishlist($offset, $limit, $sort);
            
            $products = [];
            foreach ($results as $result) {
                // Зареждаме изображението
                $image = '';
                if ($result['image']) {
                    $this->loadModelAs('tool/image', 'imageModel');
                    $image = $this->imageModel->resize($result['image'], 600, 600);
                }

                $products[] = [
                    'product_id' => $result['product_id'],
                    'name' => $result['name'],
                    'model' => $result['model'],
                    'image' => $image,
                    'price' => $result['price'],
                    'special' => $result['special'],
                    'price_formatted' => $this->formatCurrency($result['price']),
                    'special_formatted' => $result['special'] ? $this->formatCurrency($result['special']) : null,
                    'href' => $this->getLink('product/product', 'product_id=' . $result['product_id']),
                    'date_added' => $result['date_added']
                ];
            }

            $total = $this->getWishlistTotal();
            $loaded = $offset + count($products);

            $json['success'] = true;
            $json['products'] = $products;
            $json['total'] = $total;
            $json['loaded'] = $loaded;
            $json['has_more'] = $loaded < $total;
        } else {
            throw new \Exception('Функцията за зареждане на wishlist продукти не е налична');
        }

    } catch (\Exception $e) {
        $json['success'] = false;
        $json['error'] = $e->getMessage();
    }

    $this->setJSONResponseOutput($json);
}
```

### 2. Поправен JavaScript код за правилно извикване на AJAX заявките

Актуализирах `loadMoreWishlistProducts` метода в `system/storage/theme/Frontend/View/Javascript/account.js` да определя правилния endpoint в зависимост от контекста:

```javascript
// Определяме правилния endpoint в зависимост от контекста
let endpoint = 'index.php?route=account/wishlist/loadMore';

// Ако сме в account страницата (има profile-tab елементи), използваме account/account/loadMore
if (document.querySelector('.profile-tab')) {
    endpoint = 'index.php?route=account/account/loadMore';
}

// Изпращаме AJAX заявка
this.makeAjaxRequest(endpoint, formData, ...
```

## Как работи решението

- Когато потребителят е в `/account/account` страницата и натисне "Зареди още продукти", JavaScript кодът ще извика `account/account/loadMore`
- Когато потребителят е в `/account/wishlist` страницата и натисне "Зареди още продукти", JavaScript кодът ще извика `account/wishlist/loadMore`
- И двата endpoint-а връщат същия формат данни и използват същата логика за пагинация

## Резултат

Функционалността за зареждане на още продукти от wishlist сега работи правилно в двата контекста:
1. В специализираната wishlist страница (`/account/wishlist`)
2. В общата account страница (`/account/account`) в секцията "Любими продукти"

Бутонът "Зареди още продукти" се показва само когато има повече от 8 продукта и зарежда следващите 8 продукта при всяко натискане, докато всички продукти не се покажат.
